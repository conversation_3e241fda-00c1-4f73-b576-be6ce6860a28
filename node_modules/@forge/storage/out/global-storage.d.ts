import { FetchMethod } from './index';
import { CustomEntityListOptions, ListOptions } from './query-interfaces';
import { SharedStorageAdapter } from './storage-adapter';
import type { Metrics } from '@forge/util/packages/metrics-interface';
interface ListResults {
    results: {
        key: string;
        value: any;
    }[];
    nextCursor?: string;
}
export declare type StoreType = 'typed' | 'untyped';
export declare type OperationType = 'get' | 'set' | 'query' | 'delete';
export declare class GlobalStorage implements SharedStorageAdapter {
    private apiClient;
    private readonly getMetrics;
    private readonly endpoint;
    constructor(apiClient: FetchMethod, getMetrics: () => Metrics | undefined);
    get(key: string): Promise<any>;
    getSecret(key: string): Promise<any>;
    list(options: ListOptions): Promise<ListResults>;
    listCustomEntities(options: CustomEntityListOptions): Promise<ListResults>;
    set(key: string, value: any): Promise<void>;
    setSecret(key: string, value: any): Promise<void>;
    delete(key: string): Promise<void>;
    deleteSecret(key: string): Promise<void>;
    getEntity<T>(entityName: string, entityKey: string): Promise<T>;
    setEntity<T>(entityName: string, entityKey: string, value: T): Promise<void>;
    deleteEntity(entityName: string, entityKey: string): Promise<void>;
    private getInternal;
    private getEntityInternal;
    private buildRequest;
    private query;
    private mutation;
    private wrapInMetric;
}
export {};
//# sourceMappingURL=global-storage.d.ts.map