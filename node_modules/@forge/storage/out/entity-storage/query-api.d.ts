import { GlobalStorage } from '../global-storage';
import { CustomEntityQueryIndexOptions, CustomEntityListOptions, SortOrder } from '../query-interfaces';
import { Result, ListResult, WherePredicate, FilterPredicate } from '../storage-adapter';
declare class CustomEntityQueryBuilder<T> {
    protected globalStorage: Pick<GlobalStorage, 'listCustomEntities'>;
    protected queryOptions: CustomEntityListOptions;
    constructor(globalStorage: Pick<GlobalStorage, 'listCustomEntities'>, queryOptions?: CustomEntityListOptions);
    private clone;
    where(condition: WherePredicate): this;
    sort(sort: SortOrder): this;
    cursor(cursor: string): this;
    limit(limit: number): this;
    getOne(): Promise<Result<T> | undefined>;
    getMany(): Promise<ListResult<T>>;
}
declare class CustomEntityAndFilterQueryBuilder<T> extends CustomEntityQueryBuilder<T> {
    protected globalStorage: Pick<GlobalStorage, 'listCustomEntities'>;
    protected queryOptions: CustomEntityListOptions;
    constructor(globalStorage: Pick<GlobalStorage, 'listCustomEntities'>, queryOptions?: CustomEntityListOptions);
    andFilter(field: string, condition: FilterPredicate): CustomEntityAndFilterQueryBuilder<T>;
}
declare class CustomEntityOrFilterQueryBuilder<T> extends CustomEntityQueryBuilder<T> {
    protected globalStorage: Pick<GlobalStorage, 'listCustomEntities'>;
    protected queryOptions: CustomEntityListOptions;
    constructor(globalStorage: Pick<GlobalStorage, 'listCustomEntities'>, queryOptions?: CustomEntityListOptions);
    orFilter(field: string, condition: FilterPredicate): CustomEntityOrFilterQueryBuilder<T>;
}
declare class CustomEntityFilterQueryBuilder<T> extends CustomEntityQueryBuilder<T> {
    protected globalStorage: Pick<GlobalStorage, 'listCustomEntities'>;
    protected queryOptions: CustomEntityListOptions;
    constructor(globalStorage: Pick<GlobalStorage, 'listCustomEntities'>, queryOptions?: CustomEntityListOptions);
    andFilter(field: string, condition: FilterPredicate): CustomEntityAndFilterQueryBuilder<T>;
    orFilter(field: string, condition: FilterPredicate): CustomEntityOrFilterQueryBuilder<T>;
}
export declare class CustomEntityIndexBuilder<T> {
    protected globalStorage: Pick<GlobalStorage, 'listCustomEntities'>;
    protected queryOptions: CustomEntityListOptions;
    constructor(globalStorage: Pick<GlobalStorage, 'listCustomEntities'>, queryOptions?: CustomEntityListOptions);
    index(name: string, indexOptions?: CustomEntityQueryIndexOptions): CustomEntityFilterQueryBuilder<T>;
}
export declare class CustomEntityBuilder<T> {
    protected globalStorage: Pick<GlobalStorage, 'listCustomEntities'>;
    protected queryOptions: CustomEntityListOptions;
    constructor(globalStorage: Pick<GlobalStorage, 'listCustomEntities'>, queryOptions?: CustomEntityListOptions);
    entity(name: string): CustomEntityIndexBuilder<T>;
}
export {};
//# sourceMappingURL=query-api.d.ts.map