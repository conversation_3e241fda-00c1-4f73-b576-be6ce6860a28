"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityStorageBuilder = void 0;
const query_api_1 = require("./query-api");
class EntityStorageBuilder {
    entityName;
    globalStorage;
    constructor(entityName, globalStorage) {
        this.entityName = entityName;
        this.globalStorage = globalStorage;
    }
    query() {
        return new query_api_1.CustomEntityBuilder(this.globalStorage).entity(this.entityName);
    }
    get(entityKey) {
        return this.globalStorage.getEntity(this.entityName, entityKey);
    }
    set(entityKey, value) {
        return this.globalStorage.setEntity(this.entityName, entityKey, value);
    }
    delete(entityKey) {
        return this.globalStorage.deleteEntity(this.entityName, entityKey);
    }
}
exports.EntityStorageBuilder = EntityStorageBuilder;
