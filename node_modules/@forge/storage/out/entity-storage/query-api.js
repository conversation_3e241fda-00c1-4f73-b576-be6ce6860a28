"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomEntityBuilder = exports.CustomEntityIndexBuilder = void 0;
class CustomEntityQueryBuilder {
    globalStorage;
    queryOptions;
    constructor(globalStorage, queryOptions = {}) {
        this.globalStorage = globalStorage;
        this.queryOptions = queryOptions;
        this.queryOptions = {
            ...queryOptions
        };
    }
    clone(overrides) {
        return new (Object.getPrototypeOf(this).constructor)(this.globalStorage, {
            ...this.queryOptions,
            ...overrides
        });
    }
    where(condition) {
        return this.clone({
            range: {
                ...condition
            }
        });
    }
    sort(sort) {
        return this.clone({
            sort
        });
    }
    cursor(cursor) {
        return this.clone({
            cursor
        });
    }
    limit(limit) {
        return this.clone({
            limit
        });
    }
    async getOne() {
        const { results } = await this.limit(1).getMany();
        return results?.[0];
    }
    async getMany() {
        if (!this.queryOptions.entityName) {
            throw new Error('entityName is mandatory');
        }
        if (!this.queryOptions.indexName) {
            throw new Error('indexName is mandatory');
        }
        const queryOptions = { ...this.queryOptions };
        if (!queryOptions.filterOperator && queryOptions.filters) {
            queryOptions.filterOperator = 'and';
        }
        return this.globalStorage.listCustomEntities(this.queryOptions);
    }
}
class CustomEntityAndFilterQueryBuilder extends CustomEntityQueryBuilder {
    globalStorage;
    queryOptions;
    constructor(globalStorage, queryOptions = {}) {
        super(globalStorage, queryOptions);
        this.globalStorage = globalStorage;
        this.queryOptions = queryOptions;
        this.queryOptions = {
            ...queryOptions
        };
    }
    andFilter(field, condition) {
        const newQueryOptions = {
            ...this.queryOptions
        };
        newQueryOptions.filters = [...(this.queryOptions.filters ?? []), { property: field, ...condition }];
        newQueryOptions.filterOperator = 'and';
        return new CustomEntityAndFilterQueryBuilder(this.globalStorage, newQueryOptions);
    }
}
class CustomEntityOrFilterQueryBuilder extends CustomEntityQueryBuilder {
    globalStorage;
    queryOptions;
    constructor(globalStorage, queryOptions = {}) {
        super(globalStorage, queryOptions);
        this.globalStorage = globalStorage;
        this.queryOptions = queryOptions;
        this.queryOptions = {
            ...queryOptions
        };
    }
    orFilter(field, condition) {
        const newQueryOptions = {
            ...this.queryOptions
        };
        newQueryOptions.filters = [...(this.queryOptions.filters ?? []), { property: field, ...condition }];
        newQueryOptions.filterOperator = 'or';
        return new CustomEntityOrFilterQueryBuilder(this.globalStorage, newQueryOptions);
    }
}
class CustomEntityFilterQueryBuilder extends CustomEntityQueryBuilder {
    globalStorage;
    queryOptions;
    constructor(globalStorage, queryOptions = {}) {
        super(globalStorage, queryOptions);
        this.globalStorage = globalStorage;
        this.queryOptions = queryOptions;
        this.queryOptions = {
            ...queryOptions
        };
    }
    andFilter(field, condition) {
        return new CustomEntityAndFilterQueryBuilder(this.globalStorage, this.queryOptions).andFilter(field, condition);
    }
    orFilter(field, condition) {
        return new CustomEntityOrFilterQueryBuilder(this.globalStorage, this.queryOptions).orFilter(field, condition);
    }
}
class CustomEntityIndexBuilder {
    globalStorage;
    queryOptions;
    constructor(globalStorage, queryOptions = {}) {
        this.globalStorage = globalStorage;
        this.queryOptions = queryOptions;
        this.queryOptions = {
            ...queryOptions
        };
    }
    index(name, indexOptions) {
        const indexProperties = indexOptions ? { indexName: name, ...indexOptions } : { indexName: name };
        return new CustomEntityFilterQueryBuilder(this.globalStorage, {
            ...this.queryOptions,
            ...indexProperties
        });
    }
}
exports.CustomEntityIndexBuilder = CustomEntityIndexBuilder;
class CustomEntityBuilder {
    globalStorage;
    queryOptions;
    constructor(globalStorage, queryOptions = {}) {
        this.globalStorage = globalStorage;
        this.queryOptions = queryOptions;
        this.queryOptions = {
            ...queryOptions
        };
    }
    entity(name) {
        return new CustomEntityIndexBuilder(this.globalStorage, {
            ...this.queryOptions,
            entityName: name
        });
    }
}
exports.CustomEntityBuilder = CustomEntityBuilder;
