import { GlobalStorage } from '../global-storage';
import { CustomEntityIndexBuilder } from './query-api';
declare type EntityGlobalStorage = Pick<GlobalStorage, 'setEntity' | 'getEntity' | 'deleteEntity' | 'listCustomEntities'>;
export interface EntityStorageBuilderType<T> {
    query(): CustomEntityIndexBuilder<T>;
    get(entityKey: string): Promise<T | undefined>;
    set(entityKey: string, value: T): Promise<void>;
    delete(entityKey: string): Promise<void>;
}
export declare class EntityStorageBuilder<T> implements EntityStorageBuilderType<T> {
    private entityName;
    private globalStorage;
    constructor(entityName: string, globalStorage: EntityGlobalStorage);
    query(): CustomEntityIndexBuilder<T>;
    get(entityKey: string): Promise<T | undefined>;
    set(entityKey: string, value: T): Promise<void>;
    delete(entityKey: string): Promise<void>;
}
export {};
//# sourceMappingURL=storage-builder.d.ts.map