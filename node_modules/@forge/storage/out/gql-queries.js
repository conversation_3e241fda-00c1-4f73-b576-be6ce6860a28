"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomEntityQueries = exports.UntypedQueries = void 0;
class UntypedQueries {
    static get = (key, encrypted) => ({
        query: `
      query forge_app_getApplicationStorageEntity($key: ID!, $encrypted: Boolean!) {
        appStoredEntity(key: $key, encrypted: $encrypted) {
          key
          value
        }
      }
    `,
        variables: {
            key,
            encrypted
        }
    });
    static set = (key, value, encrypted) => ({
        query: `
      mutation forge_app_setApplicationStorageEntity($input: SetAppStoredEntityMutationInput!) {
        appStorage{
          setAppStoredEntity(input: $input) {
            success

            errors {
              message
              extensions {
                errorType
                statusCode
              }
            }
          }
        }
      }
    `,
        variables: {
            input: {
                key,
                value,
                encrypted
            }
        }
    });
    static delete = (key, encrypted) => ({
        query: `
      mutation forge_app_deleteApplicationStorageEntity($input: DeleteAppStoredEntityMutationInput!) {
        appStorage {
          deleteAppStoredEntity(input: $input) {
            success
  
            errors {
              message
              extensions {
                errorType
                statusCode
              }
            }
          }
        }
      }
    `,
        variables: {
            input: {
                key,
                encrypted
            }
        }
    });
    static listQuery = (options) => ({
        query: `
      query forge_app_getApplicationStorageEntities($where: [AppStoredEntityFilter!], $cursor: String, $limit: Int) {
        appStoredEntities(where: $where, after: $cursor, first: $limit) {
          edges {
            node {
              value
              key
            }
  
            cursor
          }
        }
      }
    `,
        variables: {
            where: options.where ?? null,
            cursor: options.cursor ?? null,
            limit: options.limit ?? null
        }
    });
}
exports.UntypedQueries = UntypedQueries;
class CustomEntityQueries {
    static get = (entityName, key) => ({
        query: `
    query forge_app_getApplicationStorageCustomEntity ($key: ID!, $entityName: String!) {
      appStoredCustomEntity(key: $key, entityName: $entityName) {
          value
          entityName
          key
      }
  }
    `,
        variables: {
            entityName,
            key
        }
    });
    static set = (entityName, key, value) => ({
        query: `
      mutation forge_app_setApplicationStorageCustomEntity($input: SetAppStoredCustomEntityMutationInput!) {
        appStorageCustomEntity{
          setAppStoredCustomEntity(input: $input) {
            success
  
            errors {
              message
              extensions {
                errorType
                statusCode
              }
            }
          }
        }
      }
    `,
        variables: {
            input: {
                entityName,
                key,
                value
            }
        }
    });
    static delete = (entityName, key) => ({
        query: `
      mutation forge_app_deleteApplicationStorageCustomEntity($input: DeleteAppStoredCustomEntityMutationInput!) {
        appStorageCustomEntity {
          deleteAppStoredCustomEntity(input: $input) {
            success
  
            errors {
              message
              extensions {
                errorType
                statusCode
              }
            }
          }
        }
      }
    `,
        variables: {
            input: {
                entityName,
                key
            }
        }
    });
    static listQuery = (options) => {
        return {
            query: `
      query AppStorageCustomEntityQueries ($entityName: String!, $indexName: String!, $range: AppStoredCustomEntityRange, $filters: AppStoredCustomEntityFilters, $sort:SortOrder, $limit: Int, $cursor: String, $partition: [AppStoredCustomEntityFieldValue!]) {
        appStoredCustomEntities(entityName: $entityName, indexName: $indexName, range: $range, filters: $filters, sort:$sort, limit: $limit, cursor: $cursor, partition: $partition) {
            edges {
                node {
                    key
                    value
                }
                cursor
            }
            pageInfo {
                hasNextPage
                hasPreviousPage
            }
            totalCount
            cursor
        }
  } 
      `,
            variables: {
                entityName: options.entityName,
                indexName: options.indexName,
                range: options.range,
                ...(options.filters && options.filters.length
                    ? {
                        filters: {
                            [options.filterOperator || 'and']: options.filters
                        }
                    }
                    : {}),
                ...(options.partition ? { partition: options.partition } : {}),
                ...(options.sort ? { sort: options.sort } : {}),
                ...(options.cursor ? { cursor: options.cursor } : {}),
                ...(options.limit ? { limit: options.limit } : {})
            }
        };
    };
}
exports.CustomEntityQueries = CustomEntityQueries;
