import { EntityStorageBuilderType } from './entity-storage';
import { Begins<PERSON>ithClause, BetweenClause, ExistsClause, DoesNotExistClause, GreaterThanClause, GreaterThanEqualToClause, StartsWith, NotEqualTo, In, LessThanClause, LessThanEqualToClause, ContainsClause, DoesNotContainClause, IsNotEqualToClause, EqualToClause } from './query-interfaces';
export interface StorageAdapter {
    get(key: string): Promise<any>;
    set(key: string, value: string | number | boolean | Record<string, any> | any[]): Promise<void>;
    delete(key: string): Promise<void>;
    getSecret(key: string): Promise<any>;
    setSecret(key: string, value: any): Promise<void>;
    deleteSecret(key: string): Promise<void>;
}
export interface EntityStorageAdapter {
    getEntity<T>(entityName: string, entityKey: string): Promise<T>;
    setEntity<T>(entityName: string, entityKey: string, value: T): Promise<void>;
    deleteEntity(entityName: string, entityKey: string): Promise<void>;
}
export interface EntityStorageApi {
    entity<T>(entityKey: string): EntityStorageBuilderType<T>;
}
export declare type SharedStorageAdapter = StorageAdapter & EntityStorageAdapter;
export interface QueryApi {
    query(): QueryBuilder;
}
export declare type Predicate = StartsWith | NotEqualTo | In;
export declare type Condition = Predicate;
export interface QueryBuilder {
    where(field: 'key', condition: Condition): QueryBuilder;
    cursor(cursor: string): QueryBuilder;
    limit(limit: number): QueryBuilder;
    getMany(): Promise<ListResult>;
    getOne(): Promise<Result | undefined>;
}
export declare type FilterPredicate = BetweenClause | BeginsWithClause | ExistsClause | DoesNotExistClause | GreaterThanClause | GreaterThanEqualToClause | LessThanClause | LessThanEqualToClause | ContainsClause | DoesNotContainClause | EqualToClause | IsNotEqualToClause;
export declare type WherePredicate = BetweenClause | BeginsWithClause | EqualToClause | GreaterThanClause | GreaterThanEqualToClause | LessThanClause | LessThanEqualToClause;
export interface Result<T = object> {
    key: string;
    value: T;
}
export interface ListResult<T = object> {
    results: Result<T>[];
    nextCursor?: string;
}
//# sourceMappingURL=storage-adapter.d.ts.map