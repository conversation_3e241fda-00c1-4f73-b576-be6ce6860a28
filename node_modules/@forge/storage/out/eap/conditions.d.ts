import { FilterPredicate, WherePredicate, Predicate } from '../storage-adapter';
export declare function isNotEqualTo(value: string[]): Predicate;
export declare function isIn(values: string[]): Predicate;
declare function beginsWith(value: string | number): WherePredicate;
declare function between(values: [string, string] | [number, number]): WherePredicate;
declare function exists(): FilterPredicate;
declare function doesNotExist(): FilterPredicate;
declare function isGreaterThan(value: string | number): WherePredicate;
declare function isGreaterThanEqualTo(value: string | number): WherePredicate;
declare function isLessThan(value: string | number): WherePredicate;
declare function isLessThanEqualTo(value: string | number): WherePredicate;
declare function contains(value: string): FilterPredicate;
declare function doesNotContain(value: string): FilterPredicate;
declare function equalsTo(value: number | string | boolean): WherePredicate;
declare function notEqualsTo(value: number | string | boolean): FilterPredicate;
export declare const WhereConditions: {
    beginsWith: typeof beginsWith;
    between: typeof between;
    equalsTo: typeof equalsTo;
    isGreaterThan: typeof isGreaterThan;
    isGreaterThanEqualTo: typeof isGreaterThanEqualTo;
    isLessThan: typeof isLessThan;
    isLessThanEqualTo: typeof isLessThanEqualTo;
};
export declare const FilterConditions: {
    beginsWith: typeof beginsWith;
    between: typeof between;
    contains: typeof contains;
    doesNotContain: typeof doesNotContain;
    equalsTo: typeof equalsTo;
    notEqualsTo: typeof notEqualsTo;
    exists: typeof exists;
    doesNotExist: typeof doesNotExist;
    isGreaterThan: typeof isGreaterThan;
    isGreaterThanEqualTo: typeof isGreaterThanEqualTo;
    isLessThan: typeof isLessThan;
    isLessThanEqualTo: typeof isLessThanEqualTo;
};
export {};
//# sourceMappingURL=conditions.d.ts.map