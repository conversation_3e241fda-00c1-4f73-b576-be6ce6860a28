"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DefaultQueryBuilder = void 0;
class DefaultQueryBuilder {
    globalStorage;
    queryOptions;
    constructor(globalStorage, queryOptions = {}) {
        this.globalStorage = globalStorage;
        this.queryOptions = queryOptions;
    }
    where(field, where) {
        return new DefaultQueryBuilder(this.globalStorage, {
            ...this.queryOptions,
            where: [
                {
                    field,
                    ...where
                }
            ]
        });
    }
    cursor(cursor) {
        return new DefaultQueryBuilder(this.globalStorage, {
            ...this.queryOptions,
            cursor
        });
    }
    limit(limit) {
        return new DefaultQueryBuilder(this.globalStorage, {
            ...this.queryOptions,
            limit
        });
    }
    async getOne() {
        const { results } = await this.limit(1).getMany();
        if (results && results.length > 0) {
            return results[0];
        }
    }
    async getMany() {
        return this.globalStorage.list(this.queryOptions);
    }
}
exports.DefaultQueryBuilder = DefaultQueryBuilder;
