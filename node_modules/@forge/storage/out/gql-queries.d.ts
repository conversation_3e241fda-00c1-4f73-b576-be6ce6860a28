import { CustomEntityListOptions, ListOptions } from './query-interfaces';
export declare class UntypedQueries {
    static get: (key: string, encrypted: boolean) => {
        query: string;
        variables: {
            key: string;
            encrypted: boolean;
        };
    };
    static set: (key: string, value: any, encrypted: boolean) => {
        query: string;
        variables: {
            input: {
                key: string;
                value: any;
                encrypted: boolean;
            };
        };
    };
    static delete: (key: string, encrypted: boolean) => {
        query: string;
        variables: {
            input: {
                key: string;
                encrypted: boolean;
            };
        };
    };
    static listQuery: (options: ListOptions) => {
        query: string;
        variables: {
            where: import("./query-interfaces").WhereClause[] | null;
            cursor: string | null;
            limit: number | null;
        };
    };
}
export declare class CustomEntityQueries {
    static get: (entityName: string, key: string) => {
        query: string;
        variables: {
            entityName: string;
            key: string;
        };
    };
    static set: (entityName: string, key: string, value: any) => {
        query: string;
        variables: {
            input: {
                entityName: string;
                key: string;
                value: any;
            };
        };
    };
    static delete: (entityName: string, key: string) => {
        query: string;
        variables: {
            input: {
                entityName: string;
                key: string;
            };
        };
    };
    static listQuery: (options: CustomEntityListOptions) => {
        query: string;
        variables: {
            limit?: number | undefined;
            cursor?: string | undefined;
            sort?: import("./query-interfaces").SortOrder | undefined;
            partition?: import("./query-interfaces").CustomEntityPartitionValue[] | undefined;
            filters?: {
                [x: string]: import("./query-interfaces").FilterClause[];
            } | undefined;
            entityName: string | undefined;
            indexName: string | undefined;
            range: import("./query-interfaces").RangeClause | undefined;
        };
    };
}
//# sourceMappingURL=gql-queries.d.ts.map