"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalStorage = void 0;
const errors_1 = require("./errors");
const gql_queries_1 = require("./gql-queries");
function assertNoErrors(errors) {
    if (errors && errors.length > 0) {
        const { message, extensions: { errorType } } = errors[0];
        throw errors_1.APIError.forErrorCode(errorType, message);
    }
}
async function getResponseBody(response) {
    if (response.status !== 200) {
        throw errors_1.APIError.forStatus(response.status);
    }
    const responseText = await response.text();
    let responseBody;
    try {
        responseBody = JSON.parse(responseText);
    }
    catch (error) {
        throw errors_1.APIError.forUnexpected(`Response text was not a valid JSON: ${responseText}`);
    }
    assertNoErrors(responseBody.errors);
    return responseBody.data;
}
class GlobalStorage {
    apiClient;
    getMetrics;
    endpoint = '/forge/entities/graphql';
    constructor(apiClient, getMetrics) {
        this.apiClient = apiClient;
        this.getMetrics = getMetrics;
    }
    async get(key) {
        return this.getInternal(key, false);
    }
    async getSecret(key) {
        return this.getInternal(key, true);
    }
    async list(options) {
        const requestBody = gql_queries_1.UntypedQueries.listQuery(options);
        const response = await this.wrapInMetric('untyped', 'query', false, async () => await this.query(requestBody));
        const edges = response.appStoredEntities.edges;
        const nextCursor = edges.length > 0 ? edges[edges.length - 1].cursor : undefined;
        const results = edges.map(({ node }) => node);
        return {
            results,
            nextCursor
        };
    }
    async listCustomEntities(options) {
        const requestBody = gql_queries_1.CustomEntityQueries.listQuery(options);
        const response = await this.wrapInMetric('typed', 'query', false, async () => await this.query(requestBody));
        const edges = response.appStoredCustomEntities.edges;
        const results = edges.map(({ node }) => node);
        return {
            results,
            nextCursor: response.appStoredCustomEntities.cursor || null
        };
    }
    async set(key, value) {
        const requestBody = gql_queries_1.UntypedQueries.set(key, value, false);
        await this.wrapInMetric('untyped', 'set', false, async () => await this.mutation(requestBody, 'appStorage', 'setAppStoredEntity'));
    }
    async setSecret(key, value) {
        const requestBody = gql_queries_1.UntypedQueries.set(key, value, true);
        await this.wrapInMetric('untyped', 'set', true, async () => await this.mutation(requestBody, 'appStorage', 'setAppStoredEntity'));
    }
    async delete(key) {
        const requestBody = gql_queries_1.UntypedQueries.delete(key, false);
        await this.wrapInMetric('untyped', 'delete', false, async () => this.mutation(requestBody, 'appStorage', 'deleteAppStoredEntity'));
    }
    async deleteSecret(key) {
        const requestBody = gql_queries_1.UntypedQueries.delete(key, true);
        await this.wrapInMetric('untyped', 'delete', true, async () => this.mutation(requestBody, 'appStorage', 'deleteAppStoredEntity'));
    }
    async getEntity(entityName, entityKey) {
        return this.getEntityInternal(entityName, entityKey);
    }
    async setEntity(entityName, entityKey, value) {
        const requestBody = gql_queries_1.CustomEntityQueries.set(entityName, entityKey, value);
        await this.wrapInMetric('typed', 'set', false, async () => this.mutation(requestBody, 'appStorageCustomEntity', 'setAppStoredCustomEntity'));
    }
    async deleteEntity(entityName, entityKey) {
        const requestBody = gql_queries_1.CustomEntityQueries.delete(entityName, entityKey);
        await this.wrapInMetric('typed', 'delete', false, async () => await this.mutation(requestBody, 'appStorageCustomEntity', 'deleteAppStoredCustomEntity'));
    }
    async getInternal(key, encrypted) {
        const requestBody = gql_queries_1.UntypedQueries.get(key, encrypted);
        const { appStoredEntity: { value } } = await this.wrapInMetric('untyped', 'get', encrypted, async () => await this.query(requestBody));
        return value ?? undefined;
    }
    async getEntityInternal(entityName, entityKey) {
        const requestBody = gql_queries_1.CustomEntityQueries.get(entityName, entityKey);
        const { appStoredCustomEntity: { value } } = await this.wrapInMetric('typed', 'get', false, async () => await this.query(requestBody));
        return value ?? undefined;
    }
    buildRequest(requestBody) {
        return {
            method: 'POST',
            body: JSON.stringify(requestBody),
            headers: {
                'content-type': 'application/json'
            }
        };
    }
    async query(body) {
        const response = await this.apiClient(this.endpoint, this.buildRequest(body));
        return await getResponseBody(response);
    }
    async mutation(body, namespace, mutationMethod) {
        const response = await this.apiClient(this.endpoint, this.buildRequest(body));
        const { [namespace]: { [mutationMethod]: { success, errors } } } = await getResponseBody(response);
        assertNoErrors(errors);
        if (!success) {
            throw errors_1.APIError.forStatus(500);
        }
        return response;
    }
    async wrapInMetric(store, operation, encrypted, fn) {
        const metrics = this.getMetrics();
        if (!metrics) {
            return await fn();
        }
        const timer = metrics
            .timing('forge.runtime.storage.operation.latency', { store, operation, encrypted: String(encrypted) })
            .measure();
        try {
            const result = await fn();
            timer.stop({ success: 'true' });
            metrics
                .counter('forge.runtime.storage.operation', {
                store,
                operation,
                encrypted: String(encrypted),
                success: 'true'
            })
                .incr();
            return result;
        }
        catch (error) {
            timer.stop({ success: 'false' });
            metrics
                .counter('forge.runtime.storage.operation', {
                store,
                operation,
                encrypted: String(encrypted),
                success: 'false'
            })
                .incr();
            throw error;
        }
    }
}
exports.GlobalStorage = GlobalStorage;
