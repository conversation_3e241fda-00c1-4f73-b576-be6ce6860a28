"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const errors_1 = require("../errors");
const global_storage_1 = require("../global-storage");
const gql_queries_1 = require("../gql-queries");
const mocks_1 = require("@atlassian/metrics-interface/dist/mocks");
const getStorage = (apiClientMock, metrics) => new global_storage_1.GlobalStorage(apiClientMock, () => metrics);
const getApiClientMock = (response, statusCode = 200) => {
    return jest.fn().mockReturnValue({
        ok: statusCode === 200,
        status: statusCode,
        text: jest.fn().mockResolvedValue(JSON.stringify(response))
    });
};
const getMetricMock = () => {
    const mockMetrics = new mocks_1.MockMetrics();
    const mockCounter = new mocks_1.MockCounter('');
    const mockTiming = new mocks_1.MockTiming('');
    const mockStopTiming = jest.fn();
    const mockMeasure = {
        stop: mockStopTiming
    };
    mockMetrics.counter.mockReturnValue(mockCounter);
    mockMetrics.timing.mockReturnValue(mockTiming);
    mockTiming.measure.mockReturnValue(mockMeasure);
    return {
        mockMetrics,
        mockCounter,
        mockStopTiming
    };
};
const checkMetricsFired = ({ mockMetrics, mockCounter, mockStopTiming }, { encrypted, ...restTags }, success) => {
    expect(mockMetrics.counter).toHaveBeenCalledWith('forge.runtime.storage.operation', {
        ...restTags,
        encrypted: String(encrypted),
        success: String(success)
    });
    expect(mockCounter.incr).toHaveBeenCalled();
    expect(mockMetrics.timing).toHaveBeenCalledWith('forge.runtime.storage.operation.latency', {
        ...restTags,
        encrypted: String(encrypted)
    });
    expect(mockStopTiming).toHaveBeenCalledWith({ success: String(success) });
};
const getApiClientMockInvalidJson = (response, statusCode = 200) => {
    return jest.fn().mockReturnValue({
        ok: statusCode === 200,
        status: statusCode,
        text: jest.fn().mockResolvedValue(response)
    });
};
const INVALID_CURSOR_ERROR = {
    message: 'error message',
    extensions: {
        errorType: 'INVALID_CURSOR'
    }
};
describe.each([
    ['with metrics', true],
    ['no metrics', false]
])('GlobalStorage - %s', (_, passMetrics) => {
    function verifyApiClientCalledWith(apiClientMock, variables, query) {
        expect(apiClientMock).toHaveBeenCalledWith('/forge/entities/graphql', expect.objectContaining({
            method: 'POST',
            body: expect.any(String),
            headers: {
                'content-type': 'application/json'
            }
        }));
        const [, { body }] = apiClientMock.mock.calls[0];
        const expectedBody = query ? { query, variables } : { variables };
        expect(JSON.parse(body)).toEqual(expect.objectContaining(expectedBody));
    }
    describe('get', () => {
        it('should call the storage API, passing the provided key and returning the stored value', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredEntity: {
                        value: 'testValue'
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const returnedValue = await globalStorage.get('testKey');
            verifyApiClientCalledWith(apiClientMock, {
                key: 'testKey',
                encrypted: false
            });
            expect(returnedValue).toEqual('testValue');
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'get', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should call the storage API, passing the provided key and returning undefined if the key doesnt exist', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredEntity: {
                        value: null
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const returnedValue = await globalStorage.get('testKey');
            verifyApiClientCalledWith(apiClientMock, {
                key: 'testKey',
                encrypted: false
            });
            expect(returnedValue).toEqual(undefined);
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'get', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should call the storage API, passing the provided key and returning the stored falsey value 0', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredEntity: {
                        value: 0
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const returnedValue = await globalStorage.get('testKey');
            verifyApiClientCalledWith(apiClientMock, {
                key: 'testKey',
                encrypted: false
            });
            expect(returnedValue).toEqual(0);
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'get', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should call the storage API, passing the provided key and returning the stored empty string', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredEntity: {
                        value: ''
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const returnedValue = await globalStorage.get('testKey');
            verifyApiClientCalledWith(apiClientMock, {
                key: 'testKey',
                encrypted: false
            });
            expect(returnedValue).toEqual('');
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'get', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error with the returned status for non-200 status codes', async () => {
            const apiClientMock = getApiClientMock(undefined, 400);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.get('testKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forStatus(400));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'get', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error with the returned error message for failed responses', async () => {
            const apiClientMock = getApiClientMock({
                errors: [INVALID_CURSOR_ERROR]
            }, 200);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.get('testKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forErrorCode('CURSOR_INVALID', 'error message'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'get', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the response is not a valid JSON', async () => {
            const apiClientMock = getApiClientMockInvalidJson('test', 200);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.get('testKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forUnexpected('Response text was not a valid JSON: test'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'get', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('get secret', () => {
        it('should call the storage API, passing the provided key and returning the stored value', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredEntity: {
                        value: 'testValue'
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const returnedValue = await globalStorage.getSecret('testKey');
            verifyApiClientCalledWith(apiClientMock, {
                key: 'testKey',
                encrypted: true
            });
            expect(returnedValue).toEqual('testValue');
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'get', encrypted: true }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('set', () => {
        it('should call the storage API, passing the provided key and value', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorage: {
                        setAppStoredEntity: {
                            success: true
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            await globalStorage.set('testKey', 'testValue');
            verifyApiClientCalledWith(apiClientMock, {
                input: {
                    key: 'testKey',
                    value: 'testValue',
                    encrypted: false
                }
            });
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'set', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns successful = false', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorage: {
                        setAppStoredEntity: {
                            success: false,
                            errors: [INVALID_CURSOR_ERROR]
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.set('testKey', 'testValue');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forErrorCode('INVALID_CURSOR', 'error message'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'set', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns a non 200 status code', async () => {
            const apiClientMock = getApiClientMockInvalidJson('', 400);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.set('testKey', 'testValue');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forStatus(400));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'set', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw a 500 error if success=false but no errors were returned', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorage: {
                        setAppStoredEntity: {
                            success: false
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            await expect(globalStorage.set('testKey', 'testValue')).rejects.toThrow(errors_1.APIError.forStatus(500));
            verifyApiClientCalledWith(apiClientMock, {
                input: {
                    key: 'testKey',
                    value: 'testValue',
                    encrypted: false
                }
            });
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'set', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('set secret', () => {
        it('should call the storage API, passing the provided key and value', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorage: {
                        setAppStoredEntity: {
                            success: true
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            await globalStorage.setSecret('testKey', 'testValue');
            verifyApiClientCalledWith(apiClientMock, {
                input: {
                    key: 'testKey',
                    value: 'testValue',
                    encrypted: true
                }
            });
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'set', encrypted: true }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('delete', () => {
        it('should call the storage API, passing the provided key', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorage: {
                        deleteAppStoredEntity: {
                            success: true
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            await globalStorage.delete('testKey');
            verifyApiClientCalledWith(apiClientMock, {
                input: {
                    key: 'testKey',
                    encrypted: false
                }
            });
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'delete', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns successful = false', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorage: {
                        deleteAppStoredEntity: {
                            success: false,
                            errors: [INVALID_CURSOR_ERROR]
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.delete('testKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forErrorCode('CURSOR_INVALID', 'error message'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'delete', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns a non 200 status code and has no body', async () => {
            const apiClientMock = getApiClientMockInvalidJson('', 400);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.delete('testKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forStatus(400));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'delete', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('delete secret', () => {
        it('should call the storage API, passing the provided key', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorage: {
                        deleteAppStoredEntity: {
                            success: true
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            await globalStorage.deleteSecret('testKey');
            verifyApiClientCalledWith(apiClientMock, {
                input: {
                    key: 'testKey',
                    encrypted: true
                }
            });
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'delete', encrypted: true }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('getEntity', () => {
        it('should call the storage API, passing the provided entity name and entity key and returning the stored value', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredCustomEntity: {
                        value: 'testValue'
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const returnedValue = await globalStorage.getEntity('testEntityName', 'testEntityKey');
            verifyApiClientCalledWith(apiClientMock, {
                entityName: 'testEntityName',
                key: 'testEntityKey'
            });
            expect(returnedValue).toEqual('testValue');
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'get', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should call the storage API, passing the provided entity key and returning undefined if the key doesnt exist', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredCustomEntity: {
                        value: null
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const returnedValue = await globalStorage.getEntity('testEntityName', 'testEntityKey');
            verifyApiClientCalledWith(apiClientMock, {
                entityName: 'testEntityName',
                key: 'testEntityKey'
            });
            expect(returnedValue).toEqual(undefined);
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'get', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should call the storage API, passing the provided key and returning the stored falsey value 0', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredCustomEntity: {
                        value: 0
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const returnedValue = await globalStorage.getEntity('testEntityName', 'testEntityKey');
            verifyApiClientCalledWith(apiClientMock, {
                entityName: 'testEntityName',
                key: 'testEntityKey'
            });
            expect(returnedValue).toEqual(0);
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'get', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should call the storage API, passing the provided key and returning the stored empty string', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredCustomEntity: {
                        value: ''
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const returnedValue = await globalStorage.getEntity('testEntityName', 'testEntityKey');
            verifyApiClientCalledWith(apiClientMock, {
                entityName: 'testEntityName',
                key: 'testEntityKey'
            });
            expect(returnedValue).toEqual('');
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'get', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error with the returned status for non-200 status codes', async () => {
            const apiClientMock = getApiClientMock(undefined, 400);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.getEntity('testEntityName', 'testEntityKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forStatus(400));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'get', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error with the returned error message for failed responses', async () => {
            const apiClientMock = getApiClientMock({
                errors: [INVALID_CURSOR_ERROR]
            }, 200);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.getEntity('testEntityName', 'testEntityKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forErrorCode('CURSOR_INVALID', 'error message'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'get', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the response is not a valid JSON', async () => {
            const apiClientMock = getApiClientMockInvalidJson('test', 200);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.getEntity('testEntityName', 'testEntityKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forUnexpected('Response text was not a valid JSON: test'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'get', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('setEntity', () => {
        it('should call the storage API, passing the provided entity name, entity key and value', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorageCustomEntity: {
                        setAppStoredCustomEntity: {
                            success: true
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            await globalStorage.setEntity('testEntityName', 'testEntityKey', 'testValue');
            verifyApiClientCalledWith(apiClientMock, {
                input: {
                    entityName: 'testEntityName',
                    key: 'testEntityKey',
                    value: 'testValue'
                }
            });
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'set', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns successful = false', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorageCustomEntity: {
                        setAppStoredCustomEntity: {
                            success: false,
                            errors: [INVALID_CURSOR_ERROR]
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.setEntity('testEntityName', 'testEntityKey', 'testValue');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forErrorCode('INVALID_CURSOR', 'error message'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'set', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns a non 200 status code', async () => {
            const apiClientMock = getApiClientMockInvalidJson('', 400);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.setEntity('testEntityName', 'testEntityKey', 'testValue');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forStatus(400));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'set', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw a 500 error if success=false but no errors were returned', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorageCustomEntity: {
                        setAppStoredCustomEntity: {
                            success: false
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            await expect(globalStorage.setEntity('testEntityName', 'testEntityKey', 'testValue')).rejects.toThrow(errors_1.APIError.forStatus(500));
            verifyApiClientCalledWith(apiClientMock, {
                input: {
                    entityName: 'testEntityName',
                    key: 'testEntityKey',
                    value: 'testValue'
                }
            });
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'set', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('deleteEntity', () => {
        it('should call the storage API, passing the provided entity name and key', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorageCustomEntity: {
                        deleteAppStoredCustomEntity: {
                            success: true
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            await globalStorage.deleteEntity('testEntityName', 'testEntityKey');
            verifyApiClientCalledWith(apiClientMock, {
                input: {
                    entityName: 'testEntityName',
                    key: 'testEntityKey'
                }
            });
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'delete', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns successful = false', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStorageCustomEntity: {
                        deleteAppStoredCustomEntity: {
                            success: false,
                            errors: [INVALID_CURSOR_ERROR]
                        }
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.deleteEntity('testEntityKey', 'testEntityKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forErrorCode('CURSOR_INVALID', 'error message'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'delete', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns a non 200 status code and has no body', async () => {
            const apiClientMock = getApiClientMockInvalidJson('', 400);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.deleteEntity('testEntityKey', 'testEntityKey');
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forStatus(400));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'delete', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('list', () => {
        it('should call the storage API with the provided parameters', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredEntities: {
                        edges: [
                            { node: { key: 'key1', value: 'testValue' }, cursor: 'cursor1' },
                            { node: { key: 'key2', value: 'testValue' }, cursor: 'cursor2' }
                        ]
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const where = [
                {
                    field: 'key',
                    condition: 'STARTS_WITH',
                    value: 'test'
                }
            ];
            const cursor = 'cursor';
            const limit = 10;
            const response = await globalStorage.list({ where, cursor, limit });
            verifyApiClientCalledWith(apiClientMock, {
                where,
                cursor,
                limit
            }, gql_queries_1.UntypedQueries.listQuery({}).query);
            expect(response).toEqual(expect.objectContaining({
                results: [
                    { key: 'key1', value: 'testValue' },
                    { key: 'key2', value: 'testValue' }
                ],
                nextCursor: 'cursor2'
            }));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'query', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should use default values', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredEntities: {
                        edges: []
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            await globalStorage.list({});
            verifyApiClientCalledWith(apiClientMock, {
                where: null,
                cursor: null,
                limit: null
            }, gql_queries_1.UntypedQueries.listQuery({}).query);
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'query', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should handle an empty result set', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredEntities: {
                        edges: []
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const where = [
                {
                    field: 'key',
                    condition: 'STARTS_WITH',
                    value: 'test'
                }
            ];
            const response = await globalStorage.list({ where });
            expect(response).toEqual(expect.objectContaining({
                results: [],
                nextCursor: undefined
            }));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'query', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns an error', async () => {
            const apiClientMock = getApiClientMock({
                errors: [INVALID_CURSOR_ERROR]
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.list({});
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forErrorCode('CURSOR_INVALID', 'error message'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'query', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns a non 200 status code and has no body', async () => {
            const apiClientMock = getApiClientMockInvalidJson('', 400);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.list({});
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forStatus(400));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'untyped', operation: 'query', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
    describe('listCustomEntities', () => {
        it('should use default values', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredCustomEntities: {
                        edges: []
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = await globalStorage.listCustomEntities({});
            expect(response).toMatchObject({
                results: [],
                nextCursor: null
            });
            verifyApiClientCalledWith(apiClientMock, {}, gql_queries_1.CustomEntityQueries.listQuery({}).query);
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'query', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should return cursor when results are not present', async () => {
            const apiClientMock = getApiClientMock({
                data: {
                    appStoredCustomEntities: {
                        edges: [],
                        cursor: 'DUMMY_CURSOR'
                    }
                }
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = await globalStorage.listCustomEntities({});
            expect(response).toMatchObject({
                results: [],
                nextCursor: 'DUMMY_CURSOR'
            });
            verifyApiClientCalledWith(apiClientMock, {}, gql_queries_1.CustomEntityQueries.listQuery({}).query);
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'query', encrypted: false }, true)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns an error', async () => {
            const apiClientMock = getApiClientMock({
                errors: [INVALID_CURSOR_ERROR]
            });
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.listCustomEntities({});
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forErrorCode('CURSOR_INVALID', 'error message'));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'query', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
        it('should throw an error if the storage API returns a non 200 status code and has no body', async () => {
            const apiClientMock = getApiClientMockInvalidJson('', 400);
            const metricMocks = getMetricMock();
            const globalStorage = getStorage(apiClientMock, passMetrics ? metricMocks.mockMetrics : undefined);
            const response = globalStorage.listCustomEntities({});
            expect(apiClientMock).toHaveBeenCalled();
            await expect(response).rejects.toThrow(errors_1.APIError.forStatus(400));
            passMetrics
                ? checkMetricsFired(metricMocks, { store: 'typed', operation: 'query', encrypted: false }, false)
                : expect(metricMocks.mockMetrics.counter).not.toHaveBeenCalled();
        });
    });
});
