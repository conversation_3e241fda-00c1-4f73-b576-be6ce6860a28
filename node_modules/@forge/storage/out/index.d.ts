import { EntityStorageBuilder } from './entity-storage';
import { GlobalStorage } from './global-storage';
import { DefaultQueryBuilder } from './query-api';
export interface Headers {
    append: (name: string, value: string) => void;
    delete: (name: string) => void;
    get: (name: string) => string | null;
    has: (name: string) => boolean;
    set: (name: string, value: string) => void;
    forEach: (callbackfn: (value: string, key: string) => void) => void;
}
export declare type RequestRedirect = 'error' | 'follow' | 'manual';
export interface RequestInit {
    body?: ArrayBuffer | string | URLSearchParams;
    headers?: Record<string, string>;
    method?: string;
    redirect?: RequestRedirect;
    signal?: AbortSignal;
}
interface Response {
    json: () => Promise<any>;
    text: () => Promise<string>;
    arrayBuffer: () => Promise<ArrayBuffer>;
    ok: boolean;
    status: number;
    statusText: string;
    headers: Headers;
}
export declare type APIResponse = Pick<Response, 'json' | 'text' | 'arrayBuffer' | 'ok' | 'status' | 'statusText'>;
export declare type FetchMethod = (url: string, init: RequestInit) => Promise<APIResponse>;
export declare const getStorageInstanceWithQuery: (adapter: GlobalStorage) => {
    get: (key: string) => Promise<any>;
    set: (key: string, value: any) => Promise<void>;
    delete: (key: string) => Promise<void>;
    getSecret: (key: string) => Promise<any>;
    setSecret: (key: string, value: any) => Promise<void>;
    deleteSecret: (key: string) => Promise<void>;
    query: () => DefaultQueryBuilder;
    entity: <T>(entityName: string) => EntityStorageBuilder<T>;
};
export { GlobalStorage } from './global-storage';
export { startsWith } from './conditions';
export { WhereConditions, FilterConditions } from './eap/conditions';
export { QueryBuilder, QueryApi, Condition, ListResult, Predicate, Result, EntityStorageApi, WherePredicate, FilterPredicate } from './storage-adapter';
export { EntityStorageBuilder, EntityStorageBuilderType } from './entity-storage';
export { Value, SortOrder } from './query-interfaces';
export { APIError } from './errors';
export { CustomEntityIndexBuilder } from './entity-storage/query-api';
//# sourceMappingURL=index.d.ts.map