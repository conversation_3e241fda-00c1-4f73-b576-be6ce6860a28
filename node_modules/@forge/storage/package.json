{"name": "@forge/storage", "version": "1.8.1", "description": "Forge Storage methods", "author": "Atlassian", "license": "SEE LICENSE IN LICENSE.txt", "main": "out/index.js", "types": "out/index.d.ts", "files": ["out"], "scripts": {"build": "yarn run clean && yarn run compile", "clean": "rm -rf ./out && rm -f tsconfig.tsbuildinfo", "compile": "tsc -b -v"}, "devDependencies": {"@atlassian/metrics-interface": "4.0.0", "@forge/util": "1.4.9", "@types/node": "14.18.63"}, "publishConfig": {"registry": "https://packages.atlassian.com/api/npm/npm-public/"}}