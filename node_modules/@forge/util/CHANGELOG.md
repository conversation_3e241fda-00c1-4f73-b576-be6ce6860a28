# @forge/util

## 1.4.9

### Patch Changes

- 984c454: Forcing patch bump to update internal registry

## 1.4.9-next.0

### Patch Changes

- 984c454: Forcing patch bump to update internal registry

## 1.4.8

### Patch Changes

- fa77f8d: Revert the package registry change for the package
- 503e60e: Add publish config
- 3302cc2: Updated dependencies

## 1.4.8-next.2

### Patch Changes

- fa77f8d: Revert the package registry change for the package

## 1.4.8-next.1

### Patch Changes

- 503e60e: Add publish config

## 1.4.8-next.0

### Patch Changes

- 3302cc2: Updated dependencies

## 1.4.7

### Patch Changes

- be59c90: Enforcing direct dependencies for forge-utils
- eb52ac8: Undo direct dependencies for forge-util
- a5e7647: Update license for Forge public packages

## 1.4.7-next.2

### Patch Changes

- a5e7647: Update license for Forge public packages

## 1.4.7-next.1

### Patch Changes

- eb52ac8: Undo direct dependencies for forge-util

## 1.4.7-next.0

### Patch Changes

- be59c90: Enforcing direct dependencies for forge-utils

## 1.4.6

### Patch Changes

- 26050db: Remove unneeded dependencies

## 1.4.6-next.0

### Patch Changes

- 26050db: Remove unneeded dependencies

## 1.4.5

### Patch Changes

- ffa1464: webpack upgrade to "5.94.0"

## 1.4.5-next.0

### Patch Changes

- ffa1464: webpack upgrade to "5.94.0"

## 1.4.4

### Patch Changes

- 99fb753: Bump forge util corresponding to @atlassian@ari@2.199.0 changes

## 1.4.4-next.0

### Patch Changes

- 99fb753: Bump forge util corresponding to @atlassian@ari@2.199.0 changes

## 1.4.3

### Patch Changes

- f8a4714: Use InMemoryMetrics from the Node monorepo

## 1.4.3-next.0

### Patch Changes

- f8a4714: Use InMemoryMetrics from the Node monorepo

## 1.4.2

### Patch Changes

- d76d95c: Bumping dependencies via Renovate:

  - webpack

## 1.4.2-next.0

### Patch Changes

- d76d95c: Bumping dependencies via Renovate:

  - webpack

## 1.4.1

### Patch Changes

- 5b82e7f: Bumping dependencies via Renovate:

  - webpack

## 1.4.1-next.0

### Patch Changes

- 5b82e7f: Bumping dependencies via Renovate:

  - webpack

## 1.4.0

### Minor Changes

- b3245a7: - Add environmentType, invocationId, installationId in runtime V2 context
  - Rename getRuntime to \_\_getRuntime in @forge/api
  - Add new method getAppContext in @forge/api to retrieve app context data
  - Replace @atlassian/cs-ari package with @atlassian/ari

## 1.4.0-next.0

### Minor Changes

- b3245a7: - Add environmentType, invocationId, installationId in runtime V2 context
  - Rename getRuntime to \_\_getRuntime in @forge/api
  - Add new method getAppContext in @forge/api to retrieve app context data
  - Replace @atlassian/cs-ari package with @atlassian/ari

## 1.3.3

### Patch Changes

- b5342a9: Bumping dependencies via Renovate:

  - webpack

## 1.3.3-next.0

### Patch Changes

- b5342a9d: Bumping dependencies via Renovate:

  - webpack

## 1.3.2

### Patch Changes

- 863f7eb: Bumping dependencies via Renovate:

  - webpack-cli

## 1.3.2-next.0

### Patch Changes

- 863f7eb: Bumping dependencies via Renovate:

  - webpack-cli

## 1.3.1

### Patch Changes

- 801ea11: Renovate
- 4999c4b: Renovate
- 94b724a: Renovate

## 1.3.1-next.2

### Patch Changes

- 4999c4b: Renovate

## 1.3.1-next.1

### Patch Changes

- 801ea11: Renovate

## 1.3.1-next.0

### Patch Changes

- 94b724a: Renovate

## 1.3.0

### Minor Changes

- 6715b6a: Add dependency ts-is-present

### Patch Changes

- a1424a3: Bumping dependencies via Renovate:

  - webpack-cli

- 04271c2: Bumping dependencies via Renovate:

  - webpack

## 1.3.0-next.2

### Patch Changes

- a1424a3c: Bumping dependencies via Renovate:

  - webpack-cli

## 1.3.0-next.1

### Minor Changes

- 6715b6a1: Add dependency ts-is-present

## 1.2.4-next.0

### Patch Changes

- 04271c27: Bumping dependencies via Renovate:

  - webpack

## 1.2.3

### Patch Changes

- 44c6dad: Update webpack

## 1.2.3-next.0

### Patch Changes

- 44c6dad: Update webpack

## 1.2.2

### Patch Changes

- 7100287: Reuse @forge/util dependencies from the monorepo

## 1.2.2-next.0

### Patch Changes

- 71002871: Reuse @forge/util dependencies from the monorepo

## 1.2.1

### Patch Changes

- af4c149: Update webpack-related packages and remove unused

## 1.2.1-next.0

### Patch Changes

- af4c149: Update webpack-related packages and remove unused

## 1.2.0

### Minor Changes

- 88e57a12: Add metrics interface dependency

## 1.2.0-next.0

### Minor Changes

- 88e57a1: Add metrics interface dependency

## 1.1.0

### Minor Changes

- 037c31a5: Upgrade webpack from 4 to 5 and other relavent packages to fix the issue for node17+ (openssl change)

## 1.1.0-next.0

### Minor Changes

- 037c31a: Upgrade webpack from 4 to 5 and other relavent packages to fix the issue for node17+ (openssl change)

## 1.0.1

### Patch Changes

- 4608ccd: Update version of analytics client

## 1.0.1-next.0

### Patch Changes

- 4608ccd: Update version of analytics client

## 1.0.0

### Major Changes

- 1daf2c5: Forge packages to 1.0.0 for upcoming platform GA 🎉

## 1.0.0-next.0

### Major Changes

- 1daf2c5: Forge is now generally available 🎉

## 0.4.17

### Patch Changes

- b41cc4c: Fix exported content

## 0.4.17-next.0

### Patch Changes

- b41cc4c: Fix exported content

## 0.4.16

## 0.4.15

## 0.4.14

## 0.4.13

## 0.4.12

## 0.4.11

## 0.4.10

## 0.4.9

## 0.4.8

## 0.4.7

## 0.4.6

### Patch Changes

- b652840: Bump analytics version number

## 0.4.6-next.0

### Patch Changes

- b652840: Bump analytics version number

## 0.4.5

## 0.4.4

## 0.4.3

## 0.4.2

## 0.4.1

### Patch Changes

- ea58df7:
