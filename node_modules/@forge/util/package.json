{"name": "@forge/util", "version": "1.4.9", "description": "", "scripts": {"build": "./scripts/build.sh", "pack": "yarn run build && ./scripts/pack.sh"}, "author": "Atlassian", "license": "SEE LICENSE IN LICENSE.txt", "exports": {"./packages/analytics-node-client": "./packages/analytics-node-client/src/index.js", "./packages/ari": "./packages/ari/index.js", "./packages/in-memory-metrics": "./packages/in-memory-metrics/dist/index.js", "./packages/logger-interface": "./packages/logger-interface/dist/index.js", "./packages/metrics-interface": "./packages/metrics-interface/dist/index.js"}, "devDependencies": {"webpack-cli": "^5.1.4"}, "publishConfig": {"registry": "https://packages.atlassian.com/api/npm/npm-public/"}}