{"name": "@forge/resolver", "version": "1.6.12", "description": "Forge function resolver", "author": "Atlassian", "license": "SEE LICENSE IN LICENSE.txt", "main": "out/index.js", "types": "out/index.d.ts", "scripts": {"build": "yarn run clean && yarn run compile", "clean": "rm -rf ./out && rm -f tsconfig.tsbuildinfo", "compile": "tsc -b -v"}, "dependencies": {"@forge/api": "5.3.0"}, "publishConfig": {"registry": "https://packages.atlassian.com/api/npm/npm-public/"}}