# @forge/resolver

## 1.6.12

### Patch Changes

- Updated dependencies [908388a]
  - @forge/api@5.3.0

## 1.6.12-next.0

### Patch Changes

- Updated dependencies [908388a]
  - @forge/api@5.3.0-next.0

## 1.6.11

### Patch Changes

- 984c454: Forcing patch bump to update internal registry
- Updated dependencies [984c454]
  - @forge/api@5.2.1

## 1.6.11-next.0

### Patch Changes

- 984c454: Forcing patch bump to update internal registry
- Updated dependencies [984c454]
  - @forge/api@5.2.1-next.0

## 1.6.10

### Patch Changes

- 4c38eba: Update README to include link to changelog
- Updated dependencies [04ab98e]
  - @forge/api@5.2.0

## 1.6.10-next.2

### Patch Changes

- 4c38eba: Update README to include link to changelog

## 1.6.10-next.1

### Patch Changes

- @forge/api@5.2.0-next.1

## 1.6.10-next.0

### Patch Changes

- Updated dependencies [04ab98e]
  - @forge/api@5.2.0-next.0

## 1.6.9

### Patch Changes

- 5be8343: Add installation into resolver context
- Updated dependencies [576da26]
  - @forge/api@5.1.1

## 1.6.9-next.2

### Patch Changes

- 5be8343: Add installation into resolver context

## 1.6.9-next.1

### Patch Changes

- @forge/api@5.1.1-next.1

## 1.6.9-next.0

### Patch Changes

- Updated dependencies [576da26]
  - @forge/api@5.1.1-next.0

## 1.6.8

### Patch Changes

- fa77f8d: Revert the package registry change for the package
- 503e60e: Add publish config
- 2086b3d: Remove node-fetch as a dependency
- Updated dependencies [fa77f8d]
- Updated dependencies [fa82e2a]
- Updated dependencies [a72008e]
- Updated dependencies [503e60e]
- Updated dependencies [2086b3d]
- Updated dependencies [55d56f7]
  - @forge/api@5.1.0

## 1.6.8-next.9

### Patch Changes

- fa77f8d: Revert the package registry change for the package
- Updated dependencies [fa77f8d]
  - @forge/api@5.1.0-next.9

## 1.6.8-next.8

### Patch Changes

- 503e60e: Add publish config
- Updated dependencies [503e60e]
  - @forge/api@5.1.0-next.8

## 1.6.8-next.7

### Patch Changes

- 2086b3d: Remove node-fetch as a dependency
- Updated dependencies [2086b3d]
  - @forge/api@5.1.0-next.7

## 1.6.8-next.6

### Patch Changes

- @forge/api@5.1.0-next.6

## 1.6.8-next.5

### Patch Changes

- Updated dependencies [fa82e2a]
  - @forge/api@5.1.0-next.5

## 1.6.8-next.4

### Patch Changes

- @forge/api@5.1.0-next.4

## 1.6.8-next.3

### Patch Changes

- @forge/api@5.1.0-next.3

## 1.6.8-next.2

### Patch Changes

- Updated dependencies [a72008e]
  - @forge/api@5.1.0-next.2

## 1.6.8-next.1

### Patch Changes

- @forge/api@5.0.2-next.1

## 1.6.8-next.0

### Patch Changes

- Updated dependencies [55d56f7]
  - @forge/api@5.0.2-next.0

## 1.6.7

### Patch Changes

- a5e7647: Update license for Forge public packages
- Updated dependencies [a5e7647]
  - @forge/api@5.0.1

## 1.6.7-next.3

### Patch Changes

- a5e7647: Update license for Forge public packages
- Updated dependencies [a5e7647]
  - @forge/api@5.0.1-next.3

## 1.6.7-next.2

### Patch Changes

- @forge/api@5.0.1-next.2

## 1.6.7-next.1

### Patch Changes

- @forge/api@5.0.1-next.1

## 1.6.7-next.0

### Patch Changes

- @forge/api@5.0.1-next.0

## 1.6.6

### Patch Changes

- Updated dependencies [a5a6cfb]
- Updated dependencies [05f0f1a]
  - @forge/api@5.0.0

## 1.6.6-next.1

### Patch Changes

- Updated dependencies [05f0f1a]
  - @forge/api@5.0.0-next.1

## 1.6.6-next.0

### Patch Changes

- Updated dependencies [a5a6cfb]
  - @forge/api@5.0.0-next.0

## 1.6.5

### Patch Changes

- Updated dependencies [e23f188]
- Updated dependencies [7e6baa1]
  - @forge/api@4.3.0

## 1.6.5-next.1

### Patch Changes

- Updated dependencies [e23f188]
  - @forge/api@4.3.0-next.1

## 1.6.5-next.0

### Patch Changes

- Updated dependencies [7e6baa1]
  - @forge/api@4.2.1-next.0

## 1.6.4

### Patch Changes

- Updated dependencies [c0f4d43]
  - @forge/api@4.2.0

## 1.6.4-next.2

### Patch Changes

- @forge/api@4.2.0-next.2

## 1.6.4-next.1

### Patch Changes

- @forge/api@4.2.0-next.1

## 1.6.4-next.0

### Patch Changes

- Updated dependencies [c0f4d43]
  - @forge/api@4.2.0-next.0

## 1.6.3

### Patch Changes

- Updated dependencies [01f334b]
  - @forge/api@4.1.2

## 1.6.3-next.0

### Patch Changes

- Updated dependencies [01f334b]
  - @forge/api@4.1.2-next.0

## 1.6.2

### Patch Changes

- @forge/api@4.1.1

## 1.6.2-next.0

### Patch Changes

- @forge/api@4.1.1-next.0

## 1.6.1

### Patch Changes

- Updated dependencies [a989ae4]
  - @forge/api@4.1.0

## 1.6.1-next.0

### Patch Changes

- Updated dependencies [a989ae4]
  - @forge/api@4.1.0-next.0

## 1.6.0

### Minor Changes

- 2c95b5d: Export Request, Response, and ResolverFunction types

### Patch Changes

- Updated dependencies [a17ae69]
- Updated dependencies [e069ff4]
- Updated dependencies [7e05319]
  - @forge/api@4.0.0

## 1.6.0-next.3

### Patch Changes

- Updated dependencies [e069ff4]
  - @forge/api@4.0.0-next.2

## 1.6.0-next.2

### Patch Changes

- Updated dependencies [7e05319]
  - @forge/api@4.0.0-next.1

## 1.6.0-next.1

### Minor Changes

- 2c95b5d: Export Request, Response, and ResolverFunction types

## 1.5.41-next.0

### Patch Changes

- Updated dependencies [a17ae69]
  - @forge/api@3.10.0-next.0

## 1.5.40

### Patch Changes

- Updated dependencies [d3e9d37]
- Updated dependencies [fbb27ea]
- Updated dependencies [0f75e93]
  - @forge/api@3.9.2

## 1.5.40-next.2

### Patch Changes

- Updated dependencies [0f75e93]
  - @forge/api@3.9.2-next.2

## 1.5.40-next.1

### Patch Changes

- Updated dependencies [fbb27ea]
  - @forge/api@3.9.2-next.1

## 1.5.40-next.0

### Patch Changes

- Updated dependencies [d3e9d37]
  - @forge/api@3.9.2-next.0

## 1.5.39

### Patch Changes

- Updated dependencies [6c9b381]
  - @forge/api@3.9.1

## 1.5.39-next.0

### Patch Changes

- Updated dependencies [6c9b381]
  - @forge/api@3.9.1-next.0

## 1.5.38

### Patch Changes

- Updated dependencies [1eaeb60]
- Updated dependencies [59f7240]
  - @forge/api@3.9.0

## 1.5.38-next.1

### Patch Changes

- Updated dependencies [59f7240]
  - @forge/api@3.9.0-next.1

## 1.5.38-next.0

### Patch Changes

- Updated dependencies [1eaeb60]
  - @forge/api@3.9.0-next.0

## 1.5.37

### Patch Changes

- Updated dependencies [d4f9acd]
  - @forge/api@3.8.1

## 1.5.37-next.1

### Patch Changes

- @forge/api@3.8.1-next.1

## 1.5.37-next.0

### Patch Changes

- Updated dependencies [d4f9acd]
  - @forge/api@3.8.1-next.0

## 1.5.36

### Patch Changes

- Updated dependencies [567b79a]
  - @forge/api@3.8.0

## 1.5.36-next.0

### Patch Changes

- Updated dependencies [567b79a]
  - @forge/api@3.8.0-next.0

## 1.5.35

### Patch Changes

- Updated dependencies [6b04ab2]
  - @forge/api@3.7.1

## 1.5.35-next.0

### Patch Changes

- Updated dependencies [6b04ab2]
  - @forge/api@3.7.1-next.0

## 1.5.34

### Patch Changes

- Updated dependencies [2cd49c2]
  - @forge/api@3.7.0

## 1.5.34-next.0

### Patch Changes

- Updated dependencies [2cd49c2]
  - @forge/api@3.7.0-next.0

## 1.5.33

### Patch Changes

- Updated dependencies [1588c76]
  - @forge/api@3.6.0

## 1.5.33-next.0

### Patch Changes

- Updated dependencies [1588c76]
  - @forge/api@3.6.0-next.0

## 1.5.32

### Patch Changes

- Updated dependencies [f8a4714]
- Updated dependencies [94956a5]
  - @forge/api@3.5.0

## 1.5.32-next.1

### Patch Changes

- Updated dependencies [94956a5]
  - @forge/api@3.5.0-next.1

## 1.5.32-next.0

### Patch Changes

- Updated dependencies [f8a4714]
  - @forge/api@3.4.1-next.0

## 1.5.31

### Patch Changes

- Updated dependencies [4d149f1]
  - @forge/api@3.4.0

## 1.5.31-next.0

### Patch Changes

- Updated dependencies [4d149f1]
  - @forge/api@3.4.0-next.0

## 1.5.30

### Patch Changes

- Updated dependencies [da345ba]
- Updated dependencies [67590d1]
  - @forge/api@3.3.1

## 1.5.30-next.1

### Patch Changes

- Updated dependencies [67590d1]
  - @forge/api@3.3.1-next.1

## 1.5.30-next.0

### Patch Changes

- Updated dependencies [da345ba]
  - @forge/api@3.3.1-next.0

## 1.5.29

### Patch Changes

- Updated dependencies [fc5ea37]
- Updated dependencies [119eb24]
  - @forge/api@3.3.0

## 1.5.29-next.2

### Patch Changes

- @forge/api@3.3.0-next.2

## 1.5.29-next.1

### Patch Changes

- Updated dependencies [fc5ea37]
  - @forge/api@3.3.0-next.1

## 1.5.29-next.0

### Patch Changes

- Updated dependencies [119eb24]
  - @forge/api@3.3.0-next.0

## 1.5.28

### Patch Changes

- Updated dependencies [e343baf]
- Updated dependencies [d36502b]
- Updated dependencies [ea39472]
- Updated dependencies [32fa518]
  - @forge/api@3.2.0

## 1.5.28-next.5

### Patch Changes

- Updated dependencies [ea39472]
  - @forge/api@3.2.0-next.5

## 1.5.28-next.4

### Patch Changes

- Updated dependencies [e343baf]
  - @forge/api@3.2.0-next.4

## 1.5.28-next.3

### Patch Changes

- @forge/api@3.2.0-next.3

## 1.5.28-next.2

### Patch Changes

- @forge/api@3.2.0-next.2

## 1.5.28-next.1

### Patch Changes

- Updated dependencies [d36502b]
  - @forge/api@3.2.0-next.1

## 1.5.28-next.0

### Patch Changes

- Updated dependencies [32fa518]
  - @forge/api@3.1.1-next.0

## 1.5.27

### Patch Changes

- Updated dependencies [6fadf22]
- Updated dependencies [0c0cacd]
  - @forge/api@3.1.0

## 1.5.27-next.1

### Patch Changes

- Updated dependencies [6fadf22]
  - @forge/api@3.1.0-next.1

## 1.5.27-next.0

### Patch Changes

- Updated dependencies [0c0cacd]
  - @forge/api@3.1.0-next.0

## 1.5.26

### Patch Changes

- Updated dependencies [73e1aac]
- Updated dependencies [b3245a7]
- Updated dependencies [ace184d]
- Updated dependencies [579c6a2]
- Updated dependencies [b04283b]
- Updated dependencies [f3ca9bf]
- Updated dependencies [16a7cf5]
  - @forge/api@3.0.0

## 1.5.26-next.7

### Patch Changes

- Updated dependencies [73e1aac]
  - @forge/api@3.0.0-next.7

## 1.5.26-next.6

### Patch Changes

- @forge/api@3.0.0-next.6

## 1.5.26-next.5

### Patch Changes

- Updated dependencies [b04283b]
  - @forge/api@3.0.0-next.5

## 1.5.26-next.4

### Patch Changes

- Updated dependencies [16a7cf5]
  - @forge/api@3.0.0-next.4

## 1.5.26-next.3

### Patch Changes

- Updated dependencies [f3ca9bf]
  - @forge/api@3.0.0-next.3

## 1.5.26-next.2

### Patch Changes

- Updated dependencies [579c6a2]
  - @forge/api@3.0.0-next.2

## 1.5.26-next.1

### Patch Changes

- Updated dependencies [b3245a7]
  - @forge/api@3.0.0-next.1

## 1.5.26-next.0

### Patch Changes

- Updated dependencies [ace184d]
  - @forge/api@2.23.0-next.0

## 1.5.25

### Patch Changes

- @forge/api@2.22.1

## 1.5.25-next.0

### Patch Changes

- @forge/api@2.22.1-next.0

## 1.5.24

### Patch Changes

- Updated dependencies [97bbfc10]
- Updated dependencies [01eec989]
- Updated dependencies [1fd03c6d]
- Updated dependencies [52455789]
  - @forge/api@2.22.0

## 1.5.24-next.2

### Patch Changes

- Updated dependencies [01eec989]
  - @forge/api@2.22.0-next.2

## 1.5.24-next.1

### Patch Changes

- Updated dependencies [97bbfc10]
- Updated dependencies [1fd03c6d]
- Updated dependencies [52455789]
  - @forge/api@2.22.0-next.1

## 1.5.24-next.0

### Patch Changes

- @forge/api@2.21.1-next.0

## 1.5.23

### Patch Changes

- Updated dependencies [da702738]
- Updated dependencies [f30dfa79]
  - @forge/api@2.21.0

## 1.5.23-next.1

### Patch Changes

- Updated dependencies [da702738]
  - @forge/api@2.21.0-next.1

## 1.5.23-next.0

### Patch Changes

- Updated dependencies [f30dfa79]
  - @forge/api@2.20.2-next.0

## 1.5.22

### Patch Changes

- Updated dependencies [0cdbe736]
- Updated dependencies [859ce647]
- Updated dependencies [7149d164]
  - @forge/api@2.20.1

## 1.5.22-next.4

### Patch Changes

- Updated dependencies [0cdbe73]
  - @forge/api@2.20.1-next.4

## 1.5.22-next.3

### Patch Changes

- @forge/api@2.20.1-next.3

## 1.5.22-next.2

### Patch Changes

- @forge/api@2.20.1-next.2

## 1.5.22-next.1

### Patch Changes

- Updated dependencies [7149d16]
  - @forge/api@2.20.1-next.1

## 1.5.22-next.0

### Patch Changes

- Updated dependencies [859ce64]
  - @forge/api@2.20.1-next.0

## 1.5.21

### Patch Changes

- Updated dependencies [2764115]
- Updated dependencies [189720f]
- Updated dependencies [8590410]
- Updated dependencies [cd1dbce]
- Updated dependencies [a52c60e]
- Updated dependencies [5007b9f]
  - @forge/api@2.20.0

## 1.5.21-next.6

### Patch Changes

- Updated dependencies [cd1dbce]
  - @forge/api@2.20.0-next.6

## 1.5.21-next.5

### Patch Changes

- Updated dependencies [5007b9f]
  - @forge/api@2.20.0-next.5

## 1.5.21-next.4

### Patch Changes

- Updated dependencies [a52c60e]
  - @forge/api@2.20.0-next.4

## 1.5.21-next.3

### Patch Changes

- @forge/api@2.20.0-next.3

## 1.5.21-next.2

### Patch Changes

- Updated dependencies [189720f]
  - @forge/api@2.20.0-next.2

## 1.5.21-next.1

### Patch Changes

- Updated dependencies [2764115]
  - @forge/api@2.20.0-next.1

## 1.5.21-next.0

### Patch Changes

- Updated dependencies [8590410]
  - @forge/api@2.19.5-next.0

## 1.5.20

### Patch Changes

- Updated dependencies [a16f47e1]
- Updated dependencies [85efdeba]
- Updated dependencies [c4b3c64]
  - @forge/api@2.19.4

## 1.5.20-next.5

### Patch Changes

- Updated dependencies [c4b3c64c]
  - @forge/api@2.19.4-next.5

## 1.5.20-next.4

### Patch Changes

- @forge/api@2.19.4-next.4

## 1.5.20-next.3

### Patch Changes

- @forge/api@2.19.4-next.3

## 1.5.20-next.2

### Patch Changes

- @forge/api@2.19.4-next.2

## 1.5.20-next.1

### Patch Changes

- Updated dependencies [a16f47e1]
  - @forge/api@2.19.4-next.1

## 1.5.20-next.0

### Patch Changes

- Updated dependencies [85efdeb]
  - @forge/api@2.19.4-next.0

## 1.5.19

### Patch Changes

- Updated dependencies [dd43e2f]
- Updated dependencies [c93b149]
  - @forge/api@2.19.3

## 1.5.19-next.2

### Patch Changes

- Updated dependencies [c93b149]
  - @forge/api@2.19.3-next.2

## 1.5.19-next.1

### Patch Changes

- Updated dependencies [dd43e2f]
  - @forge/api@2.19.3-next.1

## 1.5.19-next.0

### Patch Changes

- @forge/api@2.19.3-next.0

## 1.5.18

### Patch Changes

- Updated dependencies [ed2cbc8]
- Updated dependencies [9d50860]
  - @forge/api@2.19.2

## 1.5.18-next.3

### Patch Changes

- @forge/api@2.19.2-next.3

## 1.5.18-next.2

### Patch Changes

- @forge/api@2.19.2-next.2

## 1.5.18-next.1

### Patch Changes

- Updated dependencies [9d50860c]
  - @forge/api@2.19.2-next.1

## 1.5.18-next.0

### Patch Changes

- Updated dependencies [ed2cbc8c]
  - @forge/api@2.19.2-next.0

## 1.5.17

### Patch Changes

- Updated dependencies [641b0551]
- Updated dependencies [efffc256]
- Updated dependencies [c1c5fb59]
  - @forge/api@2.19.1

## 1.5.17-next.3

### Patch Changes

- @forge/api@2.19.1-next.3

## 1.5.17-next.2

### Patch Changes

- Updated dependencies [efffc256]
  - @forge/api@2.19.1-next.2

## 1.5.17-next.1

### Patch Changes

- Updated dependencies [641b0551]
  - @forge/api@2.19.1-next.1

## 1.5.17-next.0

### Patch Changes

- Updated dependencies [c1c5fb5]
  - @forge/api@2.19.1-next.0

## 1.5.16

### Patch Changes

- Updated dependencies [ccc113ec]
- Updated dependencies [e3260cf8]
- Updated dependencies [9f70463a]
- Updated dependencies [770654be]
- Updated dependencies [347359bf]
- Updated dependencies [9b9f58d3]
  - @forge/api@2.19.0

## 1.5.16-next.6

### Patch Changes

- Updated dependencies [770654be]
  - @forge/api@2.19.0-next.6

## 1.5.16-next.5

### Patch Changes

- Updated dependencies [e3260cf]
  - @forge/api@2.19.0-next.5

## 1.5.16-next.4

### Patch Changes

- Updated dependencies [347359b]
  - @forge/api@2.19.0-next.4

## 1.5.16-next.3

### Patch Changes

- Updated dependencies [ccc113ec]
  - @forge/api@2.18.6-next.3

## 1.5.16-next.2

### Patch Changes

- Updated dependencies [9f70463]
  - @forge/api@2.18.6-next.2

## 1.5.16-next.1

### Patch Changes

- @forge/api@2.18.6-next.1

## 1.5.16-next.0

### Patch Changes

- Updated dependencies [9b9f58d3]
  - @forge/api@2.18.6-next.0

## 1.5.15

### Patch Changes

- Updated dependencies [035116d2]
  - @forge/api@2.18.5

## 1.5.15-next.2

### Patch Changes

- @forge/api@2.18.5-next.2

## 1.5.15-next.1

### Patch Changes

- @forge/api@2.18.5-next.1

## 1.5.15-next.0

### Patch Changes

- Updated dependencies [035116d]
  - @forge/api@2.18.5-next.0

## 1.5.14

### Patch Changes

- Updated dependencies [4640d4fb]
- Updated dependencies [854ff5d]
  - @forge/api@2.18.4

## 1.5.14-next.3

### Patch Changes

- Updated dependencies [854ff5d]
  - @forge/api@2.18.4-next.3

## 1.5.14-next.2

### Patch Changes

- @forge/api@2.18.4-next.2

## 1.5.14-next.1

### Patch Changes

- @forge/api@2.18.4-next.1

## 1.5.14-next.0

### Patch Changes

- Updated dependencies [4640d4f]
  - @forge/api@2.18.4-next.0

## 1.5.13

### Patch Changes

- Updated dependencies [88be9538]
  - @forge/api@2.18.3

## 1.5.13-next.4

### Patch Changes

- @forge/api@2.18.3-next.4

## 1.5.13-next.3

### Patch Changes

- @forge/api@2.18.3-next.3

## 1.5.13-next.2

### Patch Changes

- @forge/api@2.18.3-next.2

## 1.5.13-next.1

### Patch Changes

- Updated dependencies [88be9538]
  - @forge/api@2.18.3-next.1

## 1.5.13-next.0

### Patch Changes

- @forge/api@2.18.3-next.0

## 1.5.12

### Patch Changes

- Updated dependencies [a006df23]
- Updated dependencies [33e6ca1e]
- Updated dependencies [607ae912]
  - @forge/api@2.18.2

## 1.5.12-next.6

### Patch Changes

- @forge/api@2.18.2-next.6

## 1.5.12-next.5

### Patch Changes

- @forge/api@2.18.2-next.5

## 1.5.12-next.4

### Patch Changes

- @forge/api@2.18.2-next.4

## 1.5.12-next.3

### Patch Changes

- @forge/api@2.18.2-next.3

## 1.5.12-next.2

### Patch Changes

- Updated dependencies [a006df23]
  - @forge/api@2.18.2-next.2

## 1.5.12-next.1

### Patch Changes

- Updated dependencies [607ae91]
  - @forge/api@2.18.2-next.1

## 1.5.12-next.0

### Patch Changes

- Updated dependencies [33e6ca1]
  - @forge/api@2.18.2-next.0

## 1.5.11

### Patch Changes

- Updated dependencies [e67ce5c]
- Updated dependencies [270b46d]
  - @forge/api@2.18.1

## 1.5.11-next.1

### Patch Changes

- Updated dependencies [e67ce5c]
  - @forge/api@2.18.1-next.1

## 1.5.11-next.0

### Patch Changes

- Updated dependencies [270b46da]
  - @forge/api@2.18.1-next.0

## 1.5.10

### Patch Changes

- Updated dependencies [7cab7f9c]
  - @forge/api@2.18.0

## 1.5.10-next.0

### Patch Changes

- Updated dependencies [7cab7f9c]
  - @forge/api@2.18.0-next.0

## 1.5.9

### Patch Changes

- Updated dependencies [3edb8a19]
- Updated dependencies [5ba685b6]
- Updated dependencies [cf453494]
- Updated dependencies [bb7b824d]
  - @forge/api@2.17.0

## 1.5.9-next.3

### Patch Changes

- Updated dependencies [bb7b824]
  - @forge/api@2.17.0-next.3

## 1.5.9-next.2

### Patch Changes

- Updated dependencies [cf453494]
  - @forge/api@2.17.0-next.2

## 1.5.9-next.1

### Patch Changes

- Updated dependencies [5ba685b6]
  - @forge/api@2.17.0-next.1

## 1.5.9-next.0

### Patch Changes

- Updated dependencies [3edb8a19]
  - @forge/api@2.17.0-next.0

## 1.5.8

### Patch Changes

- Updated dependencies [f7f52209]
- Updated dependencies [f877763a]
- Updated dependencies [c8e6b859]
  - @forge/api@2.16.0

## 1.5.8-next.2

### Patch Changes

- Updated dependencies [f7f52209]
  - @forge/api@2.16.0-next.2

## 1.5.8-next.1

### Patch Changes

- Updated dependencies [f877763a]
  - @forge/api@2.16.0-next.1

## 1.5.8-next.0

### Patch Changes

- Updated dependencies [c8e6b85]
  - @forge/api@2.16.0-next.0

## 1.5.7

### Patch Changes

- Updated dependencies [96f9f6d8]
  - @forge/api@2.15.3

## 1.5.7-next.1

### Patch Changes

- Updated dependencies [96f9f6d8]
  - @forge/api@2.15.3-next.1

## 1.5.7-next.0

### Patch Changes

- @forge/api@2.15.3-next.0

## 1.5.6

### Patch Changes

- 23a21ec: Bumping dependencies via Renovate:

  - portfinder

- 97b57e7: Bumping dependencies via Renovate:

  - unzipper
  - @types/unzipper

- Updated dependencies [23a21ec]
- Updated dependencies [97b57e7]
  - @forge/api@2.15.2

## 1.5.6-next.3

### Patch Changes

- 97b57e7: Bumping dependencies via Renovate:

  - unzipper
  - @types/unzipper

- Updated dependencies [97b57e7]
  - @forge/api@2.15.2-next.3

## 1.5.6-next.2

### Patch Changes

- @forge/api@2.15.2-next.2

## 1.5.6-next.1

### Patch Changes

- 23a21ec: Bumping dependencies via Renovate:

  - portfinder

- Updated dependencies [23a21ec]
  - @forge/api@2.15.2-next.1

## 1.5.6-next.0

### Patch Changes

- @forge/api@2.15.2-next.0

## 1.5.5

### Patch Changes

- Updated dependencies [e91cabc]
- Updated dependencies [55f66d2]
  - @forge/api@2.15.1

## 1.5.5-next.1

### Patch Changes

- Updated dependencies [e91cabc]
  - @forge/api@2.15.1-next.1

## 1.5.5-next.0

### Patch Changes

- Updated dependencies [55f66d2]
  - @forge/api@2.15.1-next.0

## 1.5.4

### Patch Changes

- Updated dependencies [e756563]
  - @forge/api@2.15.0

## 1.5.4-next.0

### Patch Changes

- Updated dependencies [e756563]
  - @forge/api@2.15.0-next.0

## 1.5.3

### Patch Changes

- Updated dependencies [526e78c]
- Updated dependencies [c5f6d56]
  - @forge/api@2.14.0

## 1.5.3-next.1

### Patch Changes

- Updated dependencies [526e78c]
  - @forge/api@2.14.0-next.1

## 1.5.3-next.0

### Patch Changes

- Updated dependencies [c5f6d56]
  - @forge/api@2.14.0-next.0

## 1.5.2

### Patch Changes

- Updated dependencies [4acdb01]
- Updated dependencies [8577200]
- Updated dependencies [0195732]
  - @forge/api@2.13.0

## 1.5.2-next.1

### Patch Changes

- Updated dependencies [4acdb01c]
- Updated dependencies [0195732c]
  - @forge/api@2.13.0-next.1

## 1.5.2-next.0

### Patch Changes

- Updated dependencies [********]
  - @forge/api@2.13.0-next.0

## 1.5.1

### Patch Changes

- b1ff382: Add accountType to ProductContext
- Updated dependencies [82ea466]
- Updated dependencies [60a3be6]
- Updated dependencies [659107b]
  - @forge/api@2.12.0

## 1.5.1-next.3

### Patch Changes

- Updated dependencies [659107b]
  - @forge/api@2.12.0-next.2

## 1.5.1-next.2

### Patch Changes

- Updated dependencies [82ea466]
  - @forge/api@2.12.0-next.1

## 1.5.1-next.1

### Patch Changes

- Updated dependencies [60a3be66]
  - @forge/api@2.12.0-next.0

## 1.5.1-next.0

### Patch Changes

- b1ff382: Add accountType to ProductContext

## 1.5.0

### Minor Changes

- bcd187e: Add accountType property to forge resolver context

### Patch Changes

- 61d41c6: Bumping dependencies via Renovate:

  - @types/react-reconciler

- ba6d381: Bumping dependencies via Renovate:

  - @types/cross-spawn

- 4a070c4: Bumping dependencies via Renovate:

  - command-exists

- 37eee39: Bumping dependencies via Renovate:

  - ajv

- 444ada4: Bumping dependencies via Renovate:

  - @changesets/types

- 66da5d9: Bumping dependencies via Renovate:

  - conf

- decbf80: Bumping dependencies via Renovate:

  - @types/react

- 2b1589f: Bumping dependencies via Renovate:

  - @types/minimatch

- 6386221: Bumping dependencies via Renovate:

  - @types/lodash

- 203f465: Bumping dependencies via Renovate:

  - @types/inquirer

- 51d8225: Bumping dependencies via Renovate:

  - @types/xml

- c810119: Bumping dependencies via Renovate:

  - @atlassian/logger-interface

- d6d7226: Bumping dependencies via Renovate:

  - @types/history

- 5d6f948: Bumping dependencies via Renovate:

  - @changesets/apply-release-plan

- 0d52cdb: Bumping dependencies via Renovate:

  - @types/uuid

- 1508104: Bumping dependencies via Renovate:

  - @types/cheerio

- ff5d0f8: Bumping dependencies via Renovate:

  - @types/supertest

- 8970da8: Bumping dependencies via Renovate:

  - @types/concat-stream

- Updated dependencies [61d41c6]
- Updated dependencies [ba6d381]
- Updated dependencies [4a070c4]
- Updated dependencies [37eee39]
- Updated dependencies [444ada4]
- Updated dependencies [66da5d9]
- Updated dependencies [decbf80]
- Updated dependencies [2b1589f]
- Updated dependencies [6386221]
- Updated dependencies [203f465]
- Updated dependencies [51d8225]
- Updated dependencies [c810119]
- Updated dependencies [d6d7226]
- Updated dependencies [5d6f948]
- Updated dependencies [0d52cdb]
- Updated dependencies [1508104]
- Updated dependencies [ff5d0f8]
- Updated dependencies [8970da8]
  - @forge/api@2.11.1

## 1.5.0-next.9

### Patch Changes

- 4a070c4: Bumping dependencies via Renovate:

  - command-exists

- 66da5d9: Bumping dependencies via Renovate:

  - conf

- decbf80: Bumping dependencies via Renovate:

  - @types/react

- Updated dependencies [4a070c4]
- Updated dependencies [66da5d9]
- Updated dependencies [decbf80]
  - @forge/api@2.11.1-next.8

## 1.5.0-next.8

### Patch Changes

- 37eee39: Bumping dependencies via Renovate:

  - ajv

- Updated dependencies [37eee39]
  - @forge/api@2.11.1-next.7

## 1.5.0-next.7

### Patch Changes

- 61d41c6: Bumping dependencies via Renovate:

  - @types/react-reconciler

- ba6d381: Bumping dependencies via Renovate:

  - @types/cross-spawn

- 203f465: Bumping dependencies via Renovate:

  - @types/inquirer

- ff5d0f8: Bumping dependencies via Renovate:

  - @types/supertest

- Updated dependencies [61d41c6]
- Updated dependencies [ba6d381]
- Updated dependencies [203f465]
- Updated dependencies [ff5d0f8]
  - @forge/api@2.11.1-next.6

## 1.5.0-next.6

### Patch Changes

- 0d52cdb: Bumping dependencies via Renovate:

  - @types/uuid

- Updated dependencies [0d52cdb]
  - @forge/api@2.11.1-next.5

## 1.5.0-next.5

### Patch Changes

- 51d8225: Bumping dependencies via Renovate:

  - @types/xml

- Updated dependencies [51d8225]
  - @forge/api@2.11.1-next.4

## 1.5.0-next.4

### Minor Changes

- bcd187e: Add accountType property to forge resolver context

## 1.4.11-next.3

### Patch Changes

- 2b1589f: Bumping dependencies via Renovate:

  - @types/minimatch

- 5d6f948: Bumping dependencies via Renovate:

  - @changesets/apply-release-plan

- Updated dependencies [2b1589f]
- Updated dependencies [5d6f948]
  - @forge/api@2.11.1-next.3

## 1.4.11-next.2

### Patch Changes

- 6386221: Bumping dependencies via Renovate:

  - @types/lodash

- Updated dependencies [6386221]
  - @forge/api@2.11.1-next.2

## 1.4.11-next.1

### Patch Changes

- 444ada4: Bumping dependencies via Renovate:

  - @changesets/types

- d6d7226: Bumping dependencies via Renovate:

  - @types/history

- 8970da8: Bumping dependencies via Renovate:

  - @types/concat-stream

- Updated dependencies [444ada4]
- Updated dependencies [d6d7226]
- Updated dependencies [8970da8]
  - @forge/api@2.11.1-next.1

## 1.4.11-next.0

### Patch Changes

- c810119: Bumping dependencies via Renovate:

  - @atlassian/logger-interface

- 1508104: Bumping dependencies via Renovate:

  - @types/cheerio

- Updated dependencies [c810119]
- Updated dependencies [1508104]
  - @forge/api@2.11.1-next.0

## 1.4.10

### Patch Changes

- Updated dependencies [dfab69c]
- Updated dependencies [3a1dd86]
- Updated dependencies [ceb1141]
- Updated dependencies [df3811d]
- Updated dependencies [addbda1]
  - @forge/api@2.11.0

## 1.4.10-next.4

### Patch Changes

- Updated dependencies [ceb1141]
  - @forge/api@2.11.0-next.4

## 1.4.10-next.3

### Patch Changes

- Updated dependencies [addbda1]
  - @forge/api@2.11.0-next.3

## 1.4.10-next.2

### Patch Changes

- Updated dependencies [3a1dd86]
- Updated dependencies [df3811d]
  - @forge/api@2.11.0-next.2

## 1.4.10-next.1

### Patch Changes

- @forge/api@2.10.1-next.1

## 1.4.10-next.0

### Patch Changes

- Updated dependencies [dfab69c]
  - @forge/api@2.10.1-next.0

## 1.4.9

### Patch Changes

- Updated dependencies [37f48c5]
- Updated dependencies [e0e3587]
- Updated dependencies [ca8551d]
  - @forge/api@2.10.0

## 1.4.9-next.2

### Patch Changes

- Updated dependencies [37f48c5]
  - @forge/api@2.10.0-next.2

## 1.4.9-next.1

### Patch Changes

- Updated dependencies [e0e3587a]
  - @forge/api@2.10.0-next.1

## 1.4.9-next.0

### Patch Changes

- Updated dependencies [ca8551dd]
  - @forge/api@2.9.2-next.0

## 1.4.8

### Patch Changes

- Updated dependencies [73b929a]
- Updated dependencies [8d0dc10]
- Updated dependencies [7a4fa35]
  - @forge/api@2.9.1

## 1.4.8-next.2

### Patch Changes

- Updated dependencies [8d0dc104]
  - @forge/api@2.9.1-next.2

## 1.4.8-next.1

### Patch Changes

- Updated dependencies [73b929a]
  - @forge/api@2.9.1-next.1

## 1.4.8-next.0

### Patch Changes

- Updated dependencies [7a4fa35]
  - @forge/api@2.9.1-next.0

## 1.4.7

### Patch Changes

- Updated dependencies [e5dd325]
  - @forge/api@2.9.0

## 1.4.7-next.0

### Patch Changes

- Updated dependencies [e5dd325d]
  - @forge/api@2.9.0-next.0

## 1.4.6

### Patch Changes

- Updated dependencies [8e2a5a6]
  - @forge/api@2.8.1

## 1.4.6-next.0

### Patch Changes

- Updated dependencies [8e2a5a6]
  - @forge/api@2.8.1-next.0

## 1.4.5

### Patch Changes

- 652e9f2: Bump Typescript and ESlint to latest
- Updated dependencies [95913f6]
  - @forge/api@2.8.0

## 1.4.5-next.1

### Patch Changes

- Updated dependencies [95913f6]
  - @forge/api@2.8.0-next.0

## 1.4.5-next.0

### Patch Changes

- 652e9f23: Bump Typescript and ESlint to latest

## 1.4.4

### Patch Changes

- Updated dependencies [b3ee297]
  - @forge/api@2.7.0

## 1.4.4-next.0

### Patch Changes

- Updated dependencies [b3ee2973]
  - @forge/api@2.7.0-next.0

## 1.4.3

### Patch Changes

- @forge/api@2.6.1

## 1.4.3-next.0

### Patch Changes

- @forge/api@2.6.1-next.0

## 1.4.2

### Patch Changes

- Updated dependencies [8571c05]
  - @forge/api@2.6.0

## 1.4.2-next.0

### Patch Changes

- Updated dependencies [8571c05]
  - @forge/api@2.6.0-next.0

## 1.4.1

### Patch Changes

- Updated dependencies [70e9c8c]
- Updated dependencies [32d11d1]
  - @forge/api@2.5.0

## 1.4.1-next.1

### Patch Changes

- Updated dependencies [32d11d1]
  - @forge/api@2.5.0-next.1

## 1.4.1-next.0

### Patch Changes

- Updated dependencies [df2cd2f]
  - @forge/api@2.5.0-next.0

## 1.4.0

### Minor Changes

- 39e868f: Add optional parameter jobId into InvokePayload interface to pass jobId into consumer function context

### Patch Changes

- @forge/api@2.4.0

## 1.4.0-next.1

### Minor Changes

- 39e868f: Add optional parameter jobId into InvokePayload interface to pass jobId into consumer function context

## 1.3.6-next.0

### Patch Changes

- Updated dependencies [de02d45]
  - @forge/api@2.4.0-next.0

## 1.3.5

### Patch Changes

- Updated dependencies [0d7fe27]
- Updated dependencies [df58629]
  - @forge/api@2.3.0

## 1.3.5-next.1

### Patch Changes

- Updated dependencies [df58629]
  - @forge/api@2.3.0-next.1

## 1.3.5-next.0

### Patch Changes

- Updated dependencies [0d7fe27]
  - @forge/api@2.3.0-next.0

## 1.3.4

### Patch Changes

- @forge/api@2.2.1

## 1.3.4-next.0

### Patch Changes

- @forge/api@2.2.1-next.0

## 1.3.3

### Patch Changes

- Updated dependencies [5dcb9bd]
  - @forge/api@2.2.0

## 1.3.3-next.0

### Patch Changes

- Updated dependencies [5dcb9bd]
  - @forge/api@2.2.0-next.0

## 1.3.2

### Patch Changes

- 5ff60ec: FRGE-273 Remove engines for storage and resolver
- Updated dependencies [fef6d3a]
- Updated dependencies [85ce23a]
- Updated dependencies [339a8ad]
- Updated dependencies [390e3d0]
  - @forge/api@2.1.0

## 1.3.2-next.4

### Patch Changes

- Updated dependencies [85ce23a]
  - @forge/api@2.1.0-next.4

## 1.3.2-next.3

### Patch Changes

- Updated dependencies [390e3d0]
  - @forge/api@2.1.0-next.3

## 1.3.2-next.2

### Patch Changes

- Updated dependencies [fef6d3a]
  - @forge/api@2.1.0-next.2

## 1.3.2-next.1

### Patch Changes

- Updated dependencies [339a8ad]
  - @forge/api@2.1.0-next.1

## 1.3.2-next.0

### Patch Changes

- 5ff60ec: FRGE-273 Remove engines for storage and resolver
  - @forge/api@2.0.2-next.0

## 1.3.1

### Patch Changes

- Updated dependencies [d3d180e]
  - @forge/api@2.0.1

## 1.3.1-next.0

### Patch Changes

- Updated dependencies [d3d180e]
  - @forge/api@2.0.1-next.0

## 1.3.0

### Minor Changes

- 4ae62248a: `@forge/api` requires usage of a new route helper when calling requestJira/requestConfluence

### Patch Changes

- Updated dependencies [4ae62248a]
  - @forge/api@2.0.0

## 1.2.0

### Minor Changes

- f304376: Add secure installContext to context

### Patch Changes

- Updated dependencies [2ede277]
- Updated dependencies [b0ae6aa]
  - @forge/api@1.2.0

## 1.2.0-next.3

### Minor Changes

- f304376: Add secure installContext to context

## 1.1.3-next.2

### Patch Changes

- @forge/api@1.2.0-next.2

## 1.1.3-next.1

### Patch Changes

- Updated dependencies [2ede277]
  - @forge/api@1.2.0-next.1

## 1.1.3-next.0

### Patch Changes

- Updated dependencies [b0ae6aa]
  - @forge/api@1.1.1-next.0

## 1.1.2

### Patch Changes

- Updated dependencies [2136fd7]
- Updated dependencies [ba442a3]
  - @forge/api@1.1.0

## 1.1.2-next.2

### Patch Changes

- @forge/api@1.1.0-next.2

## 1.1.2-next.1

### Patch Changes

- Updated dependencies [ba442a3]
  - @forge/api@1.1.0-next.1

## 1.1.2-next.0

### Patch Changes

- Updated dependencies [2136fd7]
  - @forge/api@1.1.0-next.0

## 1.1.1

### Patch Changes

- a23d0c0: Internal refactor to ensure accountId and license from the backend are always prioritised over the frontend context payload

## 1.1.1-next.0

### Patch Changes

- a23d0c0: Internal refactor to ensure accountId and license from the backend are always prioritised over the frontend context payload

## 1.1.0

### Minor Changes

- Add license information to context object in resolver function parameters

## 1.0.1

### Patch Changes

- @forge/api@1.0.1

## 1.0.1-next.0

### Patch Changes

- @forge/api@1.0.1-next.0

## 1.0.0

### Major Changes

- 1daf2c5: Forge packages to 1.0.0 for upcoming platform GA 🎉

### Patch Changes

- Updated dependencies [1daf2c5]
  - @forge/api@1.0.0

## 1.0.0-next.0

### Major Changes

- 1daf2c5: Forge is now generally available 🎉

### Patch Changes

- Updated dependencies [1daf2c5]
  - @forge/api@1.0.0-next.0

## 0.2.3

### Patch Changes

- 1473fbf: Add string to response payload
- Updated dependencies [843a703]
  - @forge/api@0.7.0

## 0.2.3-next.1

### Patch Changes

- 1473fbf: Add string to response payload

## 0.2.3-next.0

### Patch Changes

- Updated dependencies [843a703]
  - @forge/api@0.7.0-next.0

## 0.2.2

### Patch Changes

- b41cc4c: Fix exported content
- 5b666a5: Fix published content
- 41dcd69: Moved CSP logic to its own package and bridge script to cli-shared
- Updated dependencies [b41cc4c]
- Updated dependencies [5b666a5]
- Updated dependencies [6f786cf]
  - @forge/api@0.6.1

## 0.2.2-next.3

### Patch Changes

- 41dcd69: Moved CSP logic to its own package and bridge script to cli-shared

## 0.2.2-next.2

### Patch Changes

- 5b666a5: Fix published content
- Updated dependencies [5b666a5]
  - @forge/api@0.6.1-next.2

## 0.2.2-next.1

### Patch Changes

- b41cc4c: Fix exported content
- Updated dependencies [b41cc4c]
  - @forge/api@0.6.1-next.1

## 0.2.2-next.0

### Patch Changes

- Updated dependencies [6f786cf]
  - @forge/api@0.6.1-next.0

## 0.2.1

### Patch Changes

- Updated dependencies [8691270]
- Updated dependencies [8ee5500]
  - @forge/api@0.6.0

## 0.2.1-next.1

### Patch Changes

- Updated dependencies [8ee5500]
  - @forge/api@0.6.0-next.1

## 0.2.1-next.0

### Patch Changes

- Updated dependencies [8691270]
  - @forge/api@0.6.0-next.0

## 0.2.0

### Minor Changes

- bb78f70: Add client side request methods for Jira and Confluence

## 0.2.0-next.0

### Minor Changes

- bb78f70: Add client side request methods for Jira and Confluence

## 0.1.0

### Minor Changes

- fff17c5: Add new Resolver class.
- 05d76b5: Update resolver and invoker API

### Patch Changes

- e7a1e4f: Remove non-transferable objects before return
- 0985fe6: Fix ResolverFunction type to include a Promise in the response + Fix getDefinitions type to not use wrong payload type
- ca989b5: Clean up error messages
- 7425057: Add error case for duplicate resolver definitions
- 2fcd284: Add error case for returning primitive values from resolver definitions

## 0.1.0-next.5

### Patch Changes

- e7a1e4f: Remove non-transferable objects before return
- 7425057: Add error case for duplicate resolver definitions

## 0.1.0-next.4

### Patch Changes

- ca989b5: Clean up error messages

## 0.1.0-next.3

### Patch Changes

- 0985fe6: Fix ResolverFunction type to include a Promise in the response + Fix getDefinitions type to not use wrong payload type

## 0.1.0-next.2

### Patch Changes

- 2fcd284: Add error case for returning primitive values from resolver definitions

## 0.1.0-next.1

### Minor Changes

- 05d76b5: Update resolver and invoker API

## 0.1.0-next.0

### Minor Changes

- fff17c5: Add new Resolver class.
