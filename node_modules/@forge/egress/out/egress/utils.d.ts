declare const sortAndGroupEgressPermissionsByDomain: (egressAddresses: string[]) => Array<string>;
export declare enum EgressType {
    FetchBackendSide = "FETCH_BACKEND_SIDE",
    FetchClientSide = "FETCH_CLIENT_SIDE",
    Fonts = "FONTS",
    Frames = "FRAMES",
    Images = "IMAGES",
    Media = "MEDIA",
    Navigation = "NAVIGATION",
    Scripts = "SCRIPTS",
    Styles = "STYLES"
}
export declare enum EgressCategory {
    ANALYTICS = "ANALYTICS"
}
export declare type EgressPermissions = {
    type: EgressType;
    addresses: string[];
    category?: EgressCategory;
    inScopeEUD?: boolean;
};
export declare type EgressPermissionsSimplified = Omit<EgressPermissions, 'inScopeEUD' | 'enabled' | 'category'>;
interface ConfigOverrides {
    ALLOW_EGRESS_ANALYTICS?: boolean;
}
export interface AppInstallationConfig {
    key: keyof ConfigOverrides;
    value: boolean;
}
declare const getEgressesBasedOnToggles: (input: {
    overrides: ConfigOverrides;
    egress: EgressPermissions[];
    installationConfig?: AppInstallationConfig[];
}) => EgressPermissionsSimplified[];
export { sortAndGroupEgressPermissionsByDomain, getEgressesBasedOnToggles };
//# sourceMappingURL=utils.d.ts.map