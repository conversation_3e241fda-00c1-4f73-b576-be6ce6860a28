"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EgressFilteringService = void 0;
const minimatch_1 = require("minimatch");
const url_parser_1 = require("./url-parser");
class EgressFilteringService {
    URLs;
    wildcardDomains;
    allowsEverything;
    constructor(allowList) {
        this.URLs = allowList.filter((domainOrURL) => !domainOrURL.startsWith('*')).map((url) => this.parseUrl(url));
        this.wildcardDomains = allowList
            .filter((domainOrURL) => domainOrURL !== '*')
            .map((url) => this.parseUrl(url))
            .filter((url) => decodeURIComponent(url.hostname).startsWith('*'));
        this.allowsEverything = allowList.includes('*');
    }
    parseUrl(url) {
        return (0, url_parser_1.parseUrl)(url);
    }
    isValidUrl(url) {
        if (this.allowsEverything) {
            return true;
        }
        const parsedUrl = this.parseUrl(url);
        return this.allowedDomain(parsedUrl, this.URLs) || this.allowedDomain(parsedUrl, this.wildcardDomains);
    }
    allowedDomain(domain, allowList) {
        return allowList
            .filter((allowed) => allowed.protocol === domain.protocol)
            .some((url) => (0, minimatch_1.minimatch)(domain.hostname, decodeURIComponent(url.hostname)));
    }
}
exports.EgressFilteringService = EgressFilteringService;
