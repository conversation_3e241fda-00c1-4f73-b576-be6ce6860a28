"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEgressesBasedOnToggles = exports.sortAndGroupEgressPermissionsByDomain = exports.EgressCategory = exports.EgressType = void 0;
const minimatch_1 = require("minimatch");
const url_parser_1 = require("./url-parser");
const sortAndGroupEgressPermissionsByDomain = (egressAddresses) => {
    if (egressAddresses?.length === 0) {
        return [];
    }
    const protocolRegex = /^(.*?:\/\/)/;
    const domains = new Set();
    const wildcardDomains = [];
    egressAddresses.forEach((item) => {
        const itemWithProtocol = protocolRegex.test(item) ? item : `https://${item}`;
        const url = (0, url_parser_1.parseUrl)(itemWithProtocol);
        if (url.hostname.startsWith('*')) {
            domains.add(url.hostname.substring(2));
            wildcardDomains.push(new minimatch_1.Minimatch(url.hostname));
        }
        else {
            domains.add(url.hostname);
        }
    });
    return [...domains].sort().reduce((grouped, domain) => {
        if (!wildcardDomains.some((wcd) => wcd.match(domain))) {
            grouped.push(domain);
        }
        return grouped;
    }, []);
};
exports.sortAndGroupEgressPermissionsByDomain = sortAndGroupEgressPermissionsByDomain;
var EgressType;
(function (EgressType) {
    EgressType["FetchBackendSide"] = "FETCH_BACKEND_SIDE";
    EgressType["FetchClientSide"] = "FETCH_CLIENT_SIDE";
    EgressType["Fonts"] = "FONTS";
    EgressType["Frames"] = "FRAMES";
    EgressType["Images"] = "IMAGES";
    EgressType["Media"] = "MEDIA";
    EgressType["Navigation"] = "NAVIGATION";
    EgressType["Scripts"] = "SCRIPTS";
    EgressType["Styles"] = "STYLES";
})(EgressType = exports.EgressType || (exports.EgressType = {}));
var EgressCategory;
(function (EgressCategory) {
    EgressCategory["ANALYTICS"] = "ANALYTICS";
})(EgressCategory = exports.EgressCategory || (exports.EgressCategory = {}));
const getEgressesBasedOnToggles = (input) => {
    const filteredEgresses = input.egress.filter((egress) => {
        if (egress.category?.toUpperCase() === EgressCategory.ANALYTICS) {
            if (input.installationConfig) {
                const analyticsConfig = input.installationConfig.find((config) => config.key.toUpperCase() === 'ALLOW_EGRESS_ANALYTICS');
                return analyticsConfig?.value !== false;
            }
            else {
                return input.overrides.ALLOW_EGRESS_ANALYTICS !== false;
            }
        }
        return true;
    });
    const egressByType = new Map();
    for (const egress of filteredEgresses) {
        if (!egressByType.has(egress.type)) {
            egressByType.set(egress.type, egress.addresses);
        }
        egressByType.set(egress.type, [...egressByType.get(egress.type), ...egress.addresses]);
    }
    return [...egressByType.entries()].map(([type, egresses]) => ({
        type,
        addresses: [...new Set(egresses)]
    }));
};
exports.getEgressesBasedOnToggles = getEgressesBasedOnToggles;
