# @forge/egress

## 1.4.1

### Patch Changes

- 984c454: Forcing patch bump to update internal registry

## 1.4.1-next.0

### Patch Changes

- 984c454: Forcing patch bump to update internal registry

## 1.4.0

### Minor Changes

- c25288c: Update getEgressesBasedOnToggles to handle installationConfig property for an extension

### Patch Changes

- f2c2405: improve UI for XPA
- fa77f8d: Revert the package registry change for the package
- 89c96bf: Make egress category value uppercase
- 503e60e: Add publish config
- 2086b3d: Remove node-fetch as a dependency

## 1.4.0-next.5

### Patch Changes

- fa77f8d: Revert the package registry change for the package

## 1.4.0-next.4

### Patch Changes

- 503e60e: Add publish config

## 1.4.0-next.3

### Patch Changes

- 2086b3d: Remove node-fetch as a dependency

## 1.4.0-next.2

### Patch Changes

- f2c2405: improve UI for XPA

## 1.4.0-next.1

### Patch Changes

- 89c96bf: Make egress category value uppercase

## 1.4.0-next.0

### Minor Changes

- c25288c: Update getEgressesBasedOnToggles to handle installationConfig property for an extension

## 1.3.1

### Patch Changes

- a5e7647: Update license for Forge public packages

## 1.3.1-next.0

### Patch Changes

- a5e7647: Update license for Forge public packages

## 1.3.0

### Minor Changes

- 2e198f0: Adding new logic to retrieve egress permissions from a list of duplicates and with override filters

## 1.3.0-next.0

### Minor Changes

- 2e198f0: Adding new logic to retrieve egress permissions from a list of duplicates and with override filters

## 1.2.13

### Patch Changes

- 882be11: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.13-next.0

### Patch Changes

- 882be11: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.12

### Patch Changes

- c714732: Remove unused .forgeignore template feature

## 1.2.12-next.0

### Patch Changes

- c714732: Remove unused .forgeignore template feature

## 1.2.11

### Patch Changes

- b45f058: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.11-next.0

### Patch Changes

- b45f058: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.10

### Patch Changes

- 2cf5ac83: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.10-next.0

### Patch Changes

- 2cf5ac83: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.9

### Patch Changes

- ea837416: Bumping dependencies via Renovate:

  - @types/ignore-walk

- 3c3f7b1b: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.9-next.1

### Patch Changes

- 3c3f7b1: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.9-next.0

### Patch Changes

- ea83741: Bumping dependencies via Renovate:

  - @types/ignore-walk

## 1.2.8

### Patch Changes

- cde16c5: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.8-next.0

### Patch Changes

- cde16c5: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.7

### Patch Changes

- a3d620b: Bumping dependencies via Renovate:

  - @types/jest

- 3a6bae4: Bumping dependencies via Renovate:

  - @types/ignore-walk

## 1.2.7-next.1

### Patch Changes

- a3d620b1: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.7-next.0

### Patch Changes

- 3a6bae44: Bumping dependencies via Renovate:

  - @types/ignore-walk

## 1.2.6

### Patch Changes

- 43121bc: Bumping dependencies via Renovate:

  - minimatch

- 0fbf747: Bumping dependencies via Renovate:

  - @types/ignore-walk

- 9d50860: Bumping dependencies via Renovate:

  - @types/node

## 1.2.6-next.2

### Patch Changes

- 43121bc3: Bumping dependencies via Renovate:

  - minimatch

## 1.2.6-next.1

### Patch Changes

- 0fbf747: Bumping dependencies via Renovate:

  - @types/ignore-walk

## 1.2.6-next.0

### Patch Changes

- 9d50860c: Bumping dependencies via Renovate:

  - @types/node

## 1.2.5

### Patch Changes

- efffc256: Bumping dependencies via Renovate:

  - @types/node

- c1c5fb59: Bumping dependencies via Renovate:

  - @types/node

- 6f4e2f01: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.5-next.2

### Patch Changes

- 6f4e2f01: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.5-next.1

### Patch Changes

- efffc256: Bumping dependencies via Renovate:

  - @types/node

## 1.2.5-next.0

### Patch Changes

- c1c5fb5: Bumping dependencies via Renovate:

  - @types/node

## 1.2.4

### Patch Changes

- ccc113ec: Bumping dependencies via Renovate:

  - @types/node

- e3260cf8: Bumping dependencies via Renovate:

  - @types/node

- bf0a343b: Bumping dependencies via Renovate:

  - @types/jest

- 9b9f58d3: Bumping dependencies via Renovate:

  - @types/node

## 1.2.4-next.3

### Patch Changes

- e3260cf: Bumping dependencies via Renovate:

  - @types/node

## 1.2.4-next.2

### Patch Changes

- ccc113ec: Bumping dependencies via Renovate:

  - @types/node

## 1.2.4-next.1

### Patch Changes

- bf0a343b: Bumping dependencies via Renovate:

  - @types/jest

## 1.2.4-next.0

### Patch Changes

- 9b9f58d3: Bumping dependencies via Renovate:

  - @types/node

## 1.2.3

### Patch Changes

- 439098ca: Add missing dev dependencies

## 1.2.3-next.0

### Patch Changes

- 439098ca: Add missing dev dependencies

## 1.2.2

### Patch Changes

- 8105c45: Bumping dependencies via Renovate:

  - @types/jest
  - jest
  - jest-environment-jsdom

## 1.2.2-next.0

### Patch Changes

- 8105c45: Bumping dependencies via Renovate:

  - @types/jest
  - jest
  - jest-environment-jsdom

## 1.2.1

### Patch Changes

- f18ccd86: Fixed bug greedily extracting the protocol from provided egress domains

## 1.2.1-next.0

### Patch Changes

- f18ccd86: Fixed bug greedily extracting the protocol from provided egress domains

## 1.2.0

### Minor Changes

- 3bdedf0: Fix URL-constructor in Firefox browsers

### Patch Changes

- 0da2b48: Removed support for the CLI running on node 14

## 1.2.0-next.1

### Minor Changes

- 3bdedf0: Fix URL-constructor in Firefox browsers

## 1.1.3-next.0

### Patch Changes

- 0da2b48: Removed support for the CLI running on node 14

## 1.1.2

### Patch Changes

- aa19308: Add browser field to package.json

## 1.1.2-next.0

### Patch Changes

- aa193085: Add browser field to package.json

## 1.1.1

### Patch Changes

- 63d168c: Fix browser compatibility

## 1.1.1-next.0

### Patch Changes

- 63d168c: Fix browser compatibility

## 1.1.0

### Minor Changes

- 174deac: Add support for browser environments

### Patch Changes

- 3c0ac54: Move egress related services out of @forge/csp into new @forge/egress package

## 1.1.0-next.1

### Minor Changes

- 174deac: Add support for browser environments

## 1.0.1-next.0

### Patch Changes

- 3c0ac54: Move egress related services out of @forge/csp into new @forge/egress package
