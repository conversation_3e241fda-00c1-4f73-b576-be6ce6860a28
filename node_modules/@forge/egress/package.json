{"name": "@forge/egress", "version": "1.4.1", "description": "Helpers and utils for egress implementation in Forge apps", "main": "out/index.js", "author": "Atlassian", "license": "SEE LICENSE IN LICENSE.txt", "browser": "out/index.js", "types": "out/index.d.ts", "scripts": {"build": "yarn run clean && yarn run compile", "compile": "tsc -b -v", "clean": "rm -rf ./out && rm -f tsconfig.tsbuildinfo"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "14.18.63"}, "dependencies": {"minimatch": "^9.0.3"}, "publishConfig": {"registry": "https://packages.atlassian.com/api/npm/npm-public/"}}