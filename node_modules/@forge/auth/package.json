{"name": "@forge/auth", "version": "0.0.8", "description": "Supports authorization for product REST API calls", "main": "out/index.js", "types": "out/index.d.ts", "license": "SEE LICENSE IN LICENSE.txt", "scripts": {"build": "yarn run clean && yarn run compile", "compile": "tsc -b -v", "clean": "rm -rf ./out && rm -f tsconfig.tsbuildinfo"}, "dependencies": {"tslib": "^2.6.2"}, "publishConfig": {"registry": "https://packages.atlassian.com/api/npm/npm-public/"}}