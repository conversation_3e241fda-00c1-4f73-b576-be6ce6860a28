# @forge/auth

## 0.0.8

### Patch Changes

- 984c454: Forcing patch bump to update internal registry

## 0.0.8-next.0

### Patch Changes

- 984c454: Forcing patch bump to update internal registry

## 0.0.7

### Patch Changes

- fa77f8d: Revert the package registry change for the package
- 503e60e: Add publish config

## 0.0.7-next.1

### Patch Changes

- fa77f8d: Revert the package registry change for the package

## 0.0.7-next.0

### Patch Changes

- 503e60e: Add publish config

## 0.0.6

### Patch Changes

- a5e7647: Update license for Forge public packages

## 0.0.6-next.0

### Patch Changes

- a5e7647: Update license for Forge public packages

## 0.0.5

### Patch Changes

- ba548d94: Bumping dependencies via Renovate:

  - tslib

## 0.0.5-next.0

### Patch Changes

- ba548d9: Bumping dependencies via Renovate:

  - tslib

## 0.0.4

### Patch Changes

- 854ff5d: Fix Authorize API on Node runtime
- f48e6d8: Bumping dependencies via Renovate:

  - tslib

## 0.0.4-next.1

### Patch Changes

- 854ff5d: Fix Authorize API on Node runtime

## 0.0.4-next.0

### Patch Changes

- f48e6d8: Bumping dependencies via Renovate:

  - tslib

## 0.0.3

### Patch Changes

- 732c136: Bumping dependencies via Renovate:

  - tslib

## 0.0.3-next.0

### Patch Changes

- 732c136a: Bumping dependencies via Renovate:

  - tslib

## 0.0.2

### Patch Changes

- e67ce5c: Fix TypeScript definitions relying on unexported types

## 0.0.2-next.0

### Patch Changes

- e67ce5c: Fix TypeScript definitions relying on unexported types

## 0.0.1

### Patch Changes

- 4eda18e: @forge/auth
- 85ce23a: Update error message and build config
- 2d3bec6: Add tslib

## 0.0.1-next.2

### Patch Changes

- 85ce23a: Update error message and build config

## 0.0.1-next.1

### Patch Changes

- 2d3bec6: Add tslib

## 0.0.1-next.0

### Patch Changes

- 4eda18e: @forge/auth
