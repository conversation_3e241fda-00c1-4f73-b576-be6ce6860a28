"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizeJiraWithFetch = exports.authorizeConfluenceWithFetch = void 0;
var confluence_1 = require("./confluence");
Object.defineProperty(exports, "authorizeConfluenceWithFetch", { enumerable: true, get: function () { return confluence_1.authorizeConfluenceWithFetch; } });
var jira_1 = require("./jira");
Object.defineProperty(exports, "authorizeJiraWithFetch", { enumerable: true, get: function () { return jira_1.authorizeJiraWithFetch; } });
