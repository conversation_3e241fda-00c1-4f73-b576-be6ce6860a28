declare const API_ISSUES_PERMISSIONS_MAP: {
    readonly canAssign: "ASSIGN_ISSUES";
    readonly canCreate: "CREATE_ISSUES";
    readonly canEdit: "EDIT_ISSUES";
    readonly canMove: "MOVE_ISSUES";
    readonly canDelete: "DELETE_ISSUES";
    readonly canAddComments: "ADD_COMMENTS";
    readonly canEditAllComments: "EDIT_ALL_COMMENTS";
    readonly canDeleteAllComments: "DELETE_ALL_COMMENTS";
    readonly canCreateAttachments: "CREATE_ATTACHMENTS";
    readonly canDeleteAllAttachments: "DELETE_ALL_ATTACHMENTS";
};
declare const API_PROJECTS_PERMISSIONS_MAP: {
    readonly canAssignIssues: "ASSIGN_ISSUES";
    readonly canCreateIssues: "CREATE_ISSUES";
    readonly canEditIssues: "EDIT_ISSUES";
    readonly canMoveIssues: "MOVE_ISSUES";
    readonly canDeleteIssues: "DELETE_ISSUES";
    readonly canAddComments: "ADD_COMMENTS";
    readonly canEditAllComments: "EDIT_ALL_COMMENTS";
    readonly canDeleteAllComments: "DELETE_ALL_COMMENTS";
    readonly canCreateAttachments: "CREATE_ATTACHMENTS";
    readonly canDeleteAllAttachments: "DELETE_ALL_ATTACHMENTS";
};
export declare type ApiIssuesPermissionsMap = typeof API_ISSUES_PERMISSIONS_MAP;
export declare type ApiProjectsPermissionsMap = typeof API_PROJECTS_PERMISSIONS_MAP;
export { API_ISSUES_PERMISSIONS_MAP, API_PROJECTS_PERMISSIONS_MAP };
//# sourceMappingURL=permissions.d.ts.map