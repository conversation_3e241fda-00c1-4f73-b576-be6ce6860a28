import { Query<PERSON><PERSON>, <PERSON><PERSON>ty<PERSON><PERSON>age<PERSON><PERSON> } from '@forge/storage';
import { authorize } from './authorization';
import { Route } from './safeUrl';
import { webTrigger, WebTriggerResponse, WebTriggerRequest, WebTriggerMethod, WebTriggerContext } from './webTrigger';
import { __requestAtlassianAsApp, __requestAtlassianAsUser } from './runtime/fetch-and-storage';
import { invokeRemote } from './api/remote';
import { __fetchProduct } from './api/fetch';
export interface RequestInit {
    body?: ArrayBuffer | string | URLSearchParams;
    headers?: Record<string, string>;
    method?: string;
    redirect?: RequestRedirect;
    signal?: AbortSignal;
}
export interface Headers {
    append: (name: string, value: string) => void;
    delete: (name: string) => void;
    get: (name: string) => string | null;
    has: (name: string) => boolean;
    set: (name: string, value: string) => void;
    forEach: (callbackfn: (value: string, key: string) => void) => void;
}
export interface Response {
    json: () => Promise<any>;
    text: () => Promise<string>;
    arrayBuffer: () => Promise<ArrayBuffer>;
    ok: boolean;
    status: number;
    statusText: string;
    headers: Headers;
}
export declare type APIResponse = Response;
export declare type FetchMethod = (url: string, init?: RequestInit) => Promise<APIResponse>;
export declare type FetchMethodAllowingRoute = (url: string | Route, init?: RequestInit) => Promise<APIResponse>;
export declare type RequestProductMethod = (url: Route, init?: RequestInit) => Promise<APIResponse>;
export declare type FetchOptions = RequestInit;
export interface RequestProductMethods {
    requestJira: RequestProductMethod;
    requestConfluence: RequestProductMethod;
    requestBitbucket: RequestProductMethod;
}
export interface GraphQLFetchMethods {
    requestGraph: (query: string, variables?: any, headers?: Record<string, any>) => Promise<APIResponse>;
}
export interface ConnectedDataFetchMethods {
    requestConnectedData: RequestProductMethod;
}
export interface ExternalAuthAccount {
    id: string;
    displayName: string;
    avatarUrl?: string;
    scopes: string[];
}
export interface ExternalAuthAccountMethods {
    hasCredentials: (scopes?: string[]) => Promise<boolean>;
    requestCredentials: (scopes?: string[]) => Promise<boolean>;
    fetch: FetchMethodAllowingRoute;
    getAccount: () => Promise<ExternalAuthAccount | undefined>;
}
export interface ExternalAuthFetchMethods extends ExternalAuthAccountMethods {
    listCredentials: () => Promise<string[]>;
    listAccounts: () => Promise<ExternalAuthAccount[]>;
    asAccount: (externalAccountId: string) => ExternalAuthAccountMethods;
}
export interface ExternalAuthFetchMethodsProvider {
    withProvider: (provider: string, remoteName?: string, tokenId?: string) => ExternalAuthFetchMethods;
}
export interface StorageMethods {
    get: (key: string) => Promise<any>;
    set: (key: string, value: string | number | boolean | Record<string, any> | any[]) => Promise<void>;
    delete: (key: string) => Promise<void>;
}
export interface StorageAPI extends StorageMethods, QueryApi, EntityStorageApi {
}
export interface ForgeStorageAPI extends StorageAPI {
    getSecret: (key: string) => Promise<any>;
    setSecret: (key: string, value: any) => Promise<void>;
    deleteSecret: (key: string) => Promise<void>;
}
export declare type AsUserFetchMethods = RequestProductMethods & GraphQLFetchMethods & ConnectedDataFetchMethods & ExternalAuthFetchMethodsProvider;
export declare type AsAppFetchMethods = RequestProductMethods & GraphQLFetchMethods & ConnectedDataFetchMethods;
export interface FetchAPI extends RequestProductMethods {
    asUser(userId?: string): AsUserFetchMethods;
    asApp(): AsAppFetchMethods;
    fetch: FetchMethodAllowingRoute;
}
export interface ForgeAPI extends FetchAPI {
    invokeRemote: typeof invokeRemote;
}
declare const asUser: (userId?: string) => AsUserFetchMethods;
declare const asApp: () => AsAppFetchMethods;
declare const fetch: FetchMethodAllowingRoute;
declare const requestJira: RequestProductMethod;
declare const requestConfluence: RequestProductMethod;
declare const requestBitbucket: RequestProductMethod;
declare const storage: ForgeStorageAPI;
declare const API: ForgeAPI;
export declare const privacy: {
    reportPersonalData: (accounts: import("./privacy").Account[]) => Promise<import("./privacy").AccountUpdate[]>;
};
export default API;
export { asUser, asApp, authorize, fetch, requestJira, requestConfluence, requestBitbucket, invokeRemote, storage, webTrigger, WebTriggerRequest, WebTriggerResponse, WebTriggerMethod, WebTriggerContext, __requestAtlassianAsApp, __requestAtlassianAsUser, __fetchProduct };
export declare const createRequestStargateAsApp: () => FetchMethod;
export { QueryBuilder, QueryApi, EntityStorageApi, Condition, ListResult, Predicate, Result, Value, WherePredicate, FilterPredicate, startsWith, WhereConditions, FilterConditions, SortOrder } from '@forge/storage';
export { route, assumeTrustedRoute, routeFromAbsolute, Route } from './safeUrl';
export { ForgeRuntime, AppContext, getAppContext, __getRuntime, bindInvocationContext } from './api/runtime';
export { NeedsAuthenticationError, NeedsAuthenticationErrorOptions, ProxyRequestError, FetchError, ExternalEndpointNotAllowedError, ProductEndpointNotAllowedError, RequestProductNotAllowedError, NotAllowedError, InvalidWorkspaceRequestedError, HttpError, FUNCTION_ERR, isForgePlatformError, isHostedCodeError, isExpectedError } from './api/errors';
export { i18n, type TranslationFunction } from './api/i18n';
//# sourceMappingURL=index.d.ts.map