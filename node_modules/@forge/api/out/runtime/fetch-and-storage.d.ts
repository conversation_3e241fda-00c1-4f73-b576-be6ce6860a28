import { FetchMethod } from '..';
import { AuthProvider } from '../api/fetch';
import type { Metrics } from '@forge/util/packages/metrics-interface';
export declare function getContextAri(): any;
export declare function getFetchAPI(): import("..").FetchAPI;
export declare function getMetrics(): Metrics | undefined;
export declare function getRequestStargate(provider: AuthProvider): FetchMethod;
export declare const __requestAtlassianAsApp: FetchMethod;
export declare const __requestAtlassianAsUser: FetchMethod;
//# sourceMappingURL=fetch-and-storage.d.ts.map