"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.__requestAtlassianAsUser = exports.__requestAtlassianAsApp = exports.getRequestStargate = exports.getMetrics = exports.getFetchAPI = exports.getContextAri = void 0;
const fetch_1 = require("../api/fetch");
const runtime_1 = require("../api/runtime");
function getContextAri() {
    return global.api?.__getAppAri?.() ?? (0, runtime_1.__getRuntime)().contextAri;
}
exports.getContextAri = getContextAri;
function getFetchAPI() {
    const { api: sandboxAPI } = global;
    if (sandboxAPI && Object.keys(sandboxAPI).length) {
        return (0, fetch_1.getSandboxRuntimeAPI)(sandboxAPI);
    }
    else {
        return (0, fetch_1.getNodeRuntimeAPI)();
    }
}
exports.getFetchAPI = getFetchAPI;
function getMetrics() {
    if (global.api) {
        return undefined;
    }
    return (0, runtime_1.__getRuntime)().metrics;
}
exports.getMetrics = getMetrics;
function getRequestStargate(provider) {
    if (provider !== 'app' && provider !== 'user') {
        throw new Error(`Unsupported provider: ${provider}`);
    }
    const sandboxApi = global.api;
    if (sandboxApi) {
        switch (provider) {
            case 'app':
                return sandboxApi.asApp().__requestAtlassian;
            case 'user':
                return sandboxApi.asUser().__requestAtlassian;
        }
    }
    return (0, fetch_1.__fetchProduct)({ provider, remote: 'stargate', type: 'fpp' });
}
exports.getRequestStargate = getRequestStargate;
exports.__requestAtlassianAsApp = getRequestStargate('app');
exports.__requestAtlassianAsUser = getRequestStargate('user');
