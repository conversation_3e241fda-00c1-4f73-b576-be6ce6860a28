export declare const authorize: () => {
    readonly onJira: (projectPermissionsInput: import("@forge/auth").ProjectPermission[]) => Promise<import("@forge/auth").ProjectPermissionResponse[]>;
    readonly onJiraProject: (projects: import("@forge/auth").Id | import("@forge/auth").Id[]) => Record<string, import("@forge/auth").PermissionCheck>;
    readonly onJiraIssue: (issues: import("@forge/auth").Id | import("@forge/auth").Id[]) => Record<string, import("@forge/auth").PermissionCheck>;
    readonly onConfluenceContent: (contentId: import("@forge/auth").ContentId) => Record<string, import("@forge/auth").PermissionCheck>;
};
//# sourceMappingURL=index.d.ts.map