"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorize = void 0;
const auth_1 = require("@forge/auth");
const __1 = require("..");
const authorize = () => {
    let accountId;
    if (global.api) {
        accountId = process.env.__CURRENT_USER_ACCOUNT_ID;
    }
    else {
        accountId = (0, __1.__getRuntime)().aaid;
    }
    if (!accountId) {
        throw new Error(`Couldn’t find the accountId of the invoking user. This API can only be used inside user-invoked modules.`);
    }
    return {
        ...(0, auth_1.authorizeConfluenceWithFetch)(async (path, opts) => {
            const res = await (0, __1.asUser)().requestConfluence((0, __1.assumeTrustedRoute)(path), opts);
            return res.json();
        }, accountId),
        ...(0, auth_1.authorizeJiraWithFetch)(async (path, opts) => {
            const res = await (0, __1.asUser)().requestJira((0, __1.assumeTrustedRoute)(path), opts);
            return res.json();
        }, accountId)
    };
};
exports.authorize = authorize;
