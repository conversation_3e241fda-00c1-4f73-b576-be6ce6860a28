"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSandboxRuntimeAPI = exports.getNodeRuntimeAPI = exports.addMagicAgent = exports.handleProxyResponseErrors = exports.getForgeProxyError = exports.fetchRemote = exports.__fetchProduct = void 0;
const _1 = require(".");
const runtime_1 = require("./runtime");
const polyfill_response_1 = require("./polyfill-response");
const errors_1 = require("./errors");
async function wrapInMetrics(options, cb) {
    const metrics = (0, runtime_1.__getRuntime)().metrics;
    metrics.counter(options.name, options.tags).incr();
    const timer = metrics.timing(options.name, options.tags).measure();
    try {
        return await cb();
    }
    finally {
        timer.stop();
    }
}
function __fetchProduct(args) {
    return async (path, init) => {
        const response = await global.__forge_fetch__({
            type: args.type,
            provider: args.provider,
            remote: args.remote,
            accountId: args.accountId
        }, path, init);
        (0, exports.handleProxyResponseErrors)(response);
        return response;
    };
}
exports.__fetchProduct = __fetchProduct;
function fetchRemote(args) {
    return async (path, init) => {
        const response = await global.__forge_fetch__({
            type: 'tpp',
            provider: args.provider,
            remote: args.remote,
            accountId: args.account
        }, path, init);
        (0, exports.handleProxyResponseErrors)(response);
        return response;
    };
}
exports.fetchRemote = fetchRemote;
function getDefaultRemote(provider) {
    const externalAuthProvider = findExternalAuthProviderConfigOrThrow(provider);
    if (!externalAuthProvider.remotes.length) {
        throw new Error(`Missing remote config for provider ${provider}`);
    }
    return externalAuthProvider.remotes[0].key;
}
function findExternalAuthProviderConfigOrThrow(provider) {
    const { externalAuth } = (0, runtime_1.__getRuntime)();
    const externalAuthProvider = externalAuth?.find((externalAuthMetaData) => {
        return externalAuthMetaData.service === provider;
    });
    if (!externalAuthProvider) {
        throw new Error(`Bad provider or missing config for provider ${provider}`);
    }
    return externalAuthProvider;
}
const ATLASSIAN_TOKEN_SERVICE_KEY = 'atlassian-token-service-key';
const getForgeProxyError = (response) => response.headers.get('forge-proxy-error');
exports.getForgeProxyError = getForgeProxyError;
const handleProxyResponseErrors = (response) => {
    const errorReason = (0, exports.getForgeProxyError)(response);
    if (errorReason) {
        if (errorReason === 'NEEDS_AUTHENTICATION_ERR') {
            throw new errors_1.NeedsAuthenticationError('Authentication Required', ATLASSIAN_TOKEN_SERVICE_KEY);
        }
        throw new errors_1.ProxyRequestError(response.status, errorReason);
    }
};
exports.handleProxyResponseErrors = handleProxyResponseErrors;
function lazyThrowNeedsAuthenticationError(serviceKey) {
    return async (scopes) => wrapInMetrics({ name: 'api.asUser.withProvider.requestCredentials', tags: { passingScopes: String(!!scopes) } }, async () => {
        throw new errors_1.NeedsAuthenticationError('Authentication Required', serviceKey, { scopes, isExpectedError: true });
    });
}
function buildExternalAuthAccountsInfo(provider, remote) {
    const { accounts } = findExternalAuthProviderConfigOrThrow(provider);
    const buildAccountModel = (account) => {
        const { externalAccountId: id, ...rest } = account;
        return { ...rest, id };
    };
    const buildExternalAuthAccountMethods = (account, outboundAuthAccountId) => ({
        hasCredentials: async (scopes) => wrapInMetrics({ name: 'api.asUser.withProvider.hasCredentials', tags: { passingScopes: String(!!scopes) } }, async () => !scopes || scopes.every((scope) => account.scopes.includes(scope))),
        requestCredentials: lazyThrowNeedsAuthenticationError(provider),
        getAccount: async () => wrapInMetrics({ name: 'api.asUser.withProvider.getAccount' }, async () => account),
        fetch: (0, _1.wrapWithRouteUnwrapper)(fetchRemote({ provider, remote: remote ?? getDefaultRemote(provider), account: outboundAuthAccountId }))
    });
    return accounts.map((account) => {
        const authAccount = buildAccountModel(account);
        return {
            account: authAccount,
            methods: buildExternalAuthAccountMethods(authAccount, account.id)
        };
    });
}
const addMagicAgent = (init, agentOverride) => ({
    ...init,
    agent: (agentOverride ?? 'FORGE_PRODUCT_REQUEST')
});
exports.addMagicAgent = addMagicAgent;
const throwNotImplementedError = () => {
    throw new Error('not implemented');
};
const withProvider = (provider, remote) => {
    const accountsInfo = buildExternalAuthAccountsInfo(provider, remote);
    const defaultAccountInfo = accountsInfo.length ? accountsInfo[0] : undefined;
    const lazyThrowNoValidCredentialsError = () => {
        return (url) => {
            throw new Error(`Fetch failed for ${remote ? `remote '${remote}', ` : ''}provider '${provider}', path '${url}' no credentials previously requested`);
        };
    };
    return {
        hasCredentials: async (scopes) => {
            return defaultAccountInfo
                ? await defaultAccountInfo.methods.hasCredentials(scopes)
                : await wrapInMetrics({ name: 'api.asUser.withProvider.hasCredentials', tags: { passingScopes: String(!!scopes) } }, async () => false);
        },
        getAccount: async () => wrapInMetrics({ name: 'api.asUser.withProvider.getAccount' }, async () => {
            return defaultAccountInfo ? defaultAccountInfo.account : undefined;
        }),
        requestCredentials: lazyThrowNeedsAuthenticationError(provider),
        listCredentials: throwNotImplementedError,
        listAccounts: async () => wrapInMetrics({ name: 'api.asUser.withProvider.listAccounts' }, async () => {
            return accountsInfo.map(({ account }) => account);
        }),
        asAccount: (externalAccountId) => {
            const accountInfo = accountsInfo.find(({ account }) => account.id === externalAccountId);
            if (!accountInfo) {
                throw new Error(`No account with ID ${externalAccountId} found for provider ${provider}`);
            }
            return accountInfo.methods;
        },
        fetch: defaultAccountInfo ? defaultAccountInfo.methods.fetch : lazyThrowNoValidCredentialsError()
    };
};
function getNodeRuntimeAPI() {
    if (global.fetch === undefined) {
        global.fetch = async () => {
            throw new Error('The fetch function is not available');
        };
    }
    return {
        fetch: (0, _1.wrapWithRouteUnwrapper)(fetch),
        requestJira: (0, _1.wrapRequestProduct)(__fetchProduct({ provider: 'none', remote: 'jira', type: 'fpp' })),
        requestConfluence: (0, _1.wrapRequestProduct)(__fetchProduct({ provider: 'none', remote: 'confluence', type: 'fpp' })),
        requestBitbucket: (0, _1.wrapRequestProduct)(__fetchProduct({ provider: 'none', remote: 'bitbucket', type: 'fpp' })),
        asUser: (userId) => ({
            requestJira: (0, _1.wrapRequestProduct)(__fetchProduct({ provider: 'user', remote: 'jira', type: 'fpp', accountId: userId })),
            requestConfluence: (0, _1.wrapRequestProduct)(__fetchProduct({ provider: 'user', remote: 'confluence', type: 'fpp', accountId: userId })),
            requestBitbucket: (0, _1.wrapRequestProduct)(__fetchProduct({ provider: 'user', remote: 'bitbucket', type: 'fpp', accountId: userId })),
            requestGraph: (0, _1.wrapRequestGraph)(__fetchProduct({ provider: 'user', remote: 'stargate', type: 'fpp', accountId: userId })),
            requestConnectedData: (0, _1.wrapRequestConnectedData)(__fetchProduct({ provider: 'user', remote: 'stargate', type: 'fpp' })),
            withProvider
        }),
        asApp: () => ({
            requestJira: (0, _1.wrapRequestProduct)(__fetchProduct({ provider: 'app', remote: 'jira', type: 'fpp' })),
            requestConfluence: (0, _1.wrapRequestProduct)(__fetchProduct({ provider: 'app', remote: 'confluence', type: 'fpp' })),
            requestBitbucket: (0, _1.wrapRequestProduct)(__fetchProduct({ provider: 'app', remote: 'bitbucket', type: 'fpp' })),
            requestGraph: (0, _1.wrapRequestGraph)(__fetchProduct({ provider: 'app', remote: 'stargate', type: 'fpp' })),
            requestConnectedData: (0, _1.wrapRequestConnectedData)(__fetchProduct({ provider: 'app', remote: 'stargate', type: 'fpp' }))
        })
    };
}
exports.getNodeRuntimeAPI = getNodeRuntimeAPI;
function getSandboxRuntimeAPI(api) {
    return (0, _1.wrapFetchApiMethods)(api, polyfill_response_1.transformResponse);
}
exports.getSandboxRuntimeAPI = getSandboxRuntimeAPI;
