import { type ForgeSupportedLocaleCode, type GetTranslationsOptions, type GetTranslationsResult } from '@forge/i18n';
export declare const resetTranslationsCache: () => void;
export declare const getTranslations: (locale: ForgeSupportedLocaleCode, options?: GetTranslationsOptions) => Promise<GetTranslationsResult>;
export declare type TranslationFunction = (i18nKey: string, defaultValue?: string) => string;
export declare const createTranslationFunction: (locale: ForgeSupportedLocaleCode) => Promise<TranslationFunction>;
export declare const i18n: {
    createTranslationFunction: (locale: ForgeSupportedLocaleCode) => Promise<TranslationFunction>;
    getTranslations: (locale: ForgeSupportedLocaleCode, options?: GetTranslationsOptions) => Promise<GetTranslationsResult>;
};
//# sourceMappingURL=i18n.d.ts.map