{"version": 3, "file": "fetch.d.ts", "sourceRoot": "", "sources": ["../../src/api/fetch.ts"], "names": [], "mappings": "AAQA,OAAO,EAIL,QAAQ,EACR,WAAW,EAEX,WAAW,EACX,QAAQ,EACT,MAAM,IAAI,CAAC;AAKZ,oBAAY,YAAY,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;AACnD,aAAK,SAAS,GAAG,MAAM,GAAG,YAAY,GAAG,UAAU,GAAG,WAAW,GAAG,KAAK,GAAG,IAAI,CAAC;AAEjF,aAAK,oBAAoB,GAAG,MAAM,CAAC;AACnC,aAAK,kBAAkB,GAAG,MAAM,CAAC;AAEjC,aAAK,SAAS,GAAG;IAAE,QAAQ,EAAE,YAAY,CAAC;IAAC,MAAM,EAAE,SAAS,CAAC;IAAC,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;IAAC,SAAS,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAC/G,aAAK,qBAAqB,GAAG;IAAE,QAAQ,EAAE,oBAAoB,CAAC;IAAC,MAAM,EAAE,kBAAkB,CAAC;IAAC,OAAO,EAAE,MAAM,CAAA;CAAE,CAAC;AAuB7G,wBAAgB,cAAc,CAAC,IAAI,EAAE,SAAS,GAAG,WAAW,CAe3D;AAED,wBAAgB,WAAW,CAAC,IAAI,EAAE,qBAAqB,GAAG,WAAW,CAgBpE;AAyBD,eAAO,MAAM,kBAAkB,aAAc,QAAQ,kBAA8C,CAAC;AACpG,eAAO,MAAM,yBAAyB,aAAc,QAAQ,KAAG,IAa9D,CAAC;AAwDF,eAAO,MAAM,aAAa,EAAE,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,aAAa,CAAC,EAAE,MAAM,KAAK,WAG1E,CAAC;AAkDH,wBAAgB,iBAAiB,IAAI,QAAQ,CAyC5C;AAED,wBAAgB,oBAAoB,CAAC,GAAG,EAAE,GAAG,GAAG,QAAQ,CAEvD"}