interface Ari {
    toString: () => string;
}
export declare type AppAri = Ari & {
    appId: string;
};
export declare const getAppAri: (appId: string) => AppAri;
export declare type EnvironmentAri = Ari & {
    environmentId: string;
};
export declare const getEnvironmentAri: (appId: string, environmentId: string) => EnvironmentAri;
export declare type InstallationAri = Ari & {
    installationId: string;
};
export declare const getInstallationAri: (installationId: string) => InstallationAri;
export {};
//# sourceMappingURL=ari.d.ts.map