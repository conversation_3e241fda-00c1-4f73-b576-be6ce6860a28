"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.i18n = exports.createTranslationFunction = exports.getTranslations = exports.resetTranslationsCache = void 0;
const i18n_1 = require("@forge/i18n");
const fs_1 = require("fs");
const path_1 = require("path");
const runtime_1 = require("./runtime");
const getI18nBundleFolderPath = () => {
    const { appCodeDir } = (0, runtime_1.__getRuntime)().container;
    return appCodeDir ? [appCodeDir, i18n_1.I18N_BUNDLE_FOLDER_NAME] : [i18n_1.I18N_BUNDLE_FOLDER_NAME];
};
const readLocaleFileContent = async (filePath) => {
    const fileContent = await fs_1.promises.readFile((0, path_1.join)(...getI18nBundleFolderPath(), filePath));
    return JSON.parse(fileContent.toString());
};
const makeResourceAccessorErrorMessage = (message) => {
    if (global.__forge_tunnel__) {
        const cliUpdateWarning = 'To access i18n resources while using `forge tunnel`, please ensure that your Forge CLI is up to date. Run `npm install -g @forge/cli` to update to the latest version.';
        return `${message}\n${cliUpdateWarning}`;
    }
    return message;
};
const resolverResourcesAccessor = {
    getI18nInfoConfig: async () => {
        try {
            const info = (await readLocaleFileContent(i18n_1.I18N_INFO_FILE_NAME));
            return info.config;
        }
        catch (error) {
            throw new i18n_1.TranslationGetterError(makeResourceAccessorErrorMessage('Failed to get i18n info config.'));
        }
    },
    getTranslationResource: async (locale) => {
        try {
            return await readLocaleFileContent(`${locale}.json`);
        }
        catch (error) {
            throw new i18n_1.TranslationGetterError(makeResourceAccessorErrorMessage(`Failed to get translation resource for locale: ${locale}.`));
        }
    }
};
const translationsFunctionCache = new Map();
const translationsGetter = new i18n_1.TranslationsGetter(resolverResourcesAccessor);
const resetTranslationsCache = () => {
    translationsGetter.reset();
    translationsFunctionCache.clear();
};
exports.resetTranslationsCache = resetTranslationsCache;
const getTranslations = async (locale, options = {
    fallback: true
}) => {
    return await translationsGetter.getTranslations(locale, options);
};
exports.getTranslations = getTranslations;
const createTranslationFunction = async (locale) => {
    let translator = translationsFunctionCache.get(locale);
    if (!translator) {
        translator = await createTranslationFunctionImpl(locale);
        translationsFunctionCache.set(locale, translator);
    }
    return translator;
};
exports.createTranslationFunction = createTranslationFunction;
const createTranslationFunctionImpl = async (locale) => {
    const translator = new i18n_1.Translator(locale, translationsGetter);
    await translator.init();
    return (i18nKey, defaultValue) => translator.translate(i18nKey) ?? defaultValue ?? i18nKey;
};
exports.i18n = {
    createTranslationFunction: exports.createTranslationFunction,
    getTranslations: exports.getTranslations
};
