"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.invokeRemote = void 0;
const errors_1 = require("./errors");
const fetch_1 = require("./fetch");
async function invokeRemote(remoteKey, options) {
    const { path, ...fetchOptions } = options;
    if (!remoteKey) {
        throw new Error('Missing remote key provided to invokeRemote');
    }
    if (!path) {
        throw new Error('Missing or empty path provided to invokeRemote');
    }
    const response = await global.__forge_fetch__({
        type: 'frc',
        remote: remoteKey
    }, path, fetchOptions);
    handleResponseErrors(response, remoteKey);
    return response;
}
exports.invokeRemote = invokeRemote;
function handleResponseErrors(response, remoteKey) {
    const forgeProxyError = (0, fetch_1.getForgeProxyError)(response);
    if (forgeProxyError === 'INVALID_REMOTE') {
        throw new errors_1.InvalidRemoteError(`Invalid remote key provided: "${remoteKey}"`, remoteKey);
    }
    (0, fetch_1.handleProxyResponseErrors)(response);
}
