"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformResponse = void 0;
const headers_utils_1 = require("headers-utils");
const transformResponse = (fetchApi) => async (url, init) => {
    const response = await fetchApi(url, init);
    return {
        ...response,
        headers: new headers_utils_1.Headers(response.headers)
    };
};
exports.transformResponse = transformResponse;
