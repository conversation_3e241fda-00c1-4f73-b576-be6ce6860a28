import { Fetch<PERSON><PERSON>, FetchMethod, RequestInit, Response } from '..';
export declare type AuthProvider = 'app' | 'user' | 'none';
declare type RemoteAPI = 'jira' | 'confluence' | 'stargate' | 'bitbucket' | 'sql' | 'os';
declare type ExternalAuthProvider = string;
declare type ExternalAuthRemote = string;
declare type FetchArgs = {
    provider: AuthProvider;
    remote: RemoteAPI;
    type: 'fpp' | 'sql' | 'os';
    accountId?: string;
};
declare type ExternalAuthFetchArgs = {
    provider: ExternalAuthProvider;
    remote: ExternalAuthRemote;
    account: string;
};
export declare function __fetchProduct(args: FetchArgs): FetchMethod;
export declare function fetchRemote(args: ExternalAuthFetchArgs): FetchMethod;
export declare const getForgeProxyError: (response: Response) => string | null;
export declare const handleProxyResponseErrors: (response: Response) => void;
export declare const addMagicAgent: (init?: RequestInit, agentOverride?: string) => RequestInit;
export declare function getNodeRuntimeAPI(): FetchAPI;
export declare function getSandboxRuntimeAPI(api: any): FetchAPI;
export {};
//# sourceMappingURL=fetch.d.ts.map