"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getInstallationAri = exports.getEnvironmentAri = exports.getAppAri = void 0;
const ari_1 = require("@forge/util/packages/ari");
const getAppAri = (appId) => ari_1.EcosystemAppAri.create({ appId });
exports.getAppAri = getAppAri;
const getEnvironmentAri = (appId, environmentId) => ari_1.EcosystemEnvironmentAri.create({ appId, environmentId });
exports.getEnvironmentAri = getEnvironmentAri;
const getInstallationAri = (installationId) => ari_1.EcosystemInstallationAri.create({ installationId });
exports.getInstallationAri = getInstallationAri;
