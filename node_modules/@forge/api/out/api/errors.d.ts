export declare const FUNCTION_ERR = "FUNCTION_ERR";
export declare const REQUEST_EGRESS_ALLOWLIST_ERR = "REQUEST_EGRESS_ALLOWLIST_ERR";
export declare const FUNCTION_FETCH_ERR = "FUNCTION_FETCH_ERR";
export declare const NEEDS_AUTHENTICATION_ERR = "NEEDS_AUTHENTICATION_ERR";
export declare const INVALID_REMOTE_ERR = "INVALID_REMOTE_ERR";
export declare const PROXY_ERR = "PROXY_ERR";
export declare function isForgePlatformError(err: Error): boolean;
export declare function isHostedCodeError(err: Error | string): boolean;
export declare function isExpectedError(err: Error): boolean;
export declare class HttpError extends Error {
    status: number | undefined;
    constructor(message: string);
}
export declare class FetchError extends Error {
    constructor(cause: string);
}
export declare class NotAllowedError extends HttpError {
    constructor(message: string);
}
export declare class ExternalEndpointNotAllowedError extends NotAllowedError {
    constructor(failedURL: string);
}
export declare class ProductEndpointNotAllowedError extends NotAllowedError {
    constructor(failedURL: string);
}
export declare class RequestProductNotAllowedError extends NotAllowedError {
    constructor(requestedProduct: string, invocationProduct: string);
}
export declare class InvalidWorkspaceRequestedError extends NotAllowedError {
    constructor(failedURL: string);
}
export interface NeedsAuthenticationErrorOptions {
    scopes?: string[];
    isExpectedError?: boolean;
}
export declare class NeedsAuthenticationError extends HttpError {
    readonly serviceKey: string;
    readonly options?: NeedsAuthenticationErrorOptions | undefined;
    constructor(error: string, serviceKey: string, options?: NeedsAuthenticationErrorOptions | undefined);
}
export declare class InvalidRemoteError extends HttpError {
    readonly remoteKey: string;
    constructor(error: string, remoteKey: string);
}
export declare class ProxyRequestError extends HttpError {
    status: number;
    errorCode: string;
    constructor(status: number, errorCode: string);
}
//# sourceMappingURL=errors.d.ts.map