import type { RequestInfo, RequestInit } from 'node-fetch';
import { <PERSON><PERSON><PERSON><PERSON>, FetchMethod, RequestProductMethod, FetchMethodAllowingRoute, APIResponse } from '..';
export declare type FetchWrapper = (fetchApi: Function) => (url: RequestInfo, init?: RequestInit) => Promise<APIResponse>;
export declare const wrapRequestGraph: (requestGraphApi: FetchMethod) => (query: string, variables?: any, headers?: {}) => Promise<import("..").Response>;
export declare const wrapRequestConnectedData: (x: FetchMethod) => RequestProductMethod;
export declare const wrapRequestProduct: (x: FetchMethod) => RequestProductMethod;
export declare const wrapWithRouteUnwrapper: (x: FetchMethod) => FetchMethodAllowingRoute;
export declare const wrapFetchApiMethods: (api: any, wrapFetch: FetchWrapper) => FetchAPI;
//# sourceMappingURL=index.d.ts.map