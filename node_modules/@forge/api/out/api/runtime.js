"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bindInvocationContext = exports.wrapInMetrics = exports.getAppContext = exports.__getRuntime = void 0;
const errors_1 = require("./errors");
const ari_1 = require("./ari");
function __getRuntime() {
    const runtime = global.__forge_runtime__;
    if (!runtime) {
        throw new Error('Forge runtime not found.');
    }
    return runtime;
}
exports.__getRuntime = __getRuntime;
function getAppContext() {
    const runtime = __getRuntime();
    const { appId, appVersion, environmentId, environmentType, invocationId, installationId, moduleKey, license, installation } = runtime.appContext;
    const invocationRemainingTimeInMillis = runtime.lambdaContext.getRemainingTimeInMillis ??
        (() => {
            throw new Error('Lambda remaining time is not available. If tunnelling, update Forge CLI to the latest version.');
        });
    return {
        appAri: (0, ari_1.getAppAri)(appId),
        appVersion,
        environmentAri: (0, ari_1.getEnvironmentAri)(appId, environmentId),
        environmentType,
        installationAri: (0, ari_1.getInstallationAri)(installationId),
        invocationId,
        invocationRemainingTimeInMillis,
        moduleKey,
        license,
        installation
    };
}
exports.getAppContext = getAppContext;
function wrapInMetrics(name, fn, { tags } = {}) {
    return async (...args) => {
        const { metrics } = __getRuntime();
        metrics.counter(name, tags).incr();
        const timer = metrics.timing(name, tags).measure();
        let success = true;
        try {
            return await fn(...args);
        }
        catch (e) {
            const undiciError = global.__forge_undici_error__;
            if (e instanceof errors_1.ProxyRequestError ||
                (undiciError && typeof undiciError === 'function' && e instanceof undiciError)) {
                success = false;
            }
            throw e;
        }
        finally {
            timer.stop({ success: success.toString() });
        }
    };
}
exports.wrapInMetrics = wrapInMetrics;
function bindInvocationContext(fn) {
    const AsyncLocalStorage = require('async_hooks').AsyncLocalStorage;
    return AsyncLocalStorage.bind(fn);
}
exports.bindInvocationContext = bindInvocationContext;
