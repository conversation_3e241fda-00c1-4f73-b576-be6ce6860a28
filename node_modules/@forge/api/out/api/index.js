"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.wrapFetchApiMethods = exports.wrapWithRouteUnwrapper = exports.wrapRequestProduct = exports.wrapRequestConnectedData = exports.wrapRequestGraph = void 0;
const safeUrl_1 = require("../safeUrl");
const wrapRequestGraph = (requestGraphApi) => (query, variables, headers = {}) => requestGraphApi('/graphql', {
    method: 'POST',
    headers: { ...headers, 'Content-Type': 'application/json' },
    body: JSON.stringify({
        query,
        ...(variables ? { variables } : {})
    })
});
exports.wrapRequestGraph = wrapRequestGraph;
const wrapRequestConnectedData = (fetch) => (path, init) => {
    const safeUrl = (0, safeUrl_1.requireSafeUrl)(path);
    return fetch(`/connected-data/${safeUrl.value.replace(/^\/+/, '')}`, init);
};
exports.wrapRequestConnectedData = wrapRequestConnectedData;
const wrapRequestProduct = (requestProduct) => (path, init) => {
    const safeUrl = (0, safeUrl_1.requireSafeUrl)(path);
    return requestProduct(safeUrl.value, init);
};
exports.wrapRequestProduct = wrapRequestProduct;
const wrapWithRouteUnwrapper = (fetch) => (path, init) => {
    const stringPath = (0, safeUrl_1.isRoute)(path) ? path.value : path;
    return fetch(stringPath, init);
};
exports.wrapWithRouteUnwrapper = wrapWithRouteUnwrapper;
const wrapFetchApiMethods = (api, wrapFetch) => {
    return {
        fetch: (0, exports.wrapWithRouteUnwrapper)(wrapFetch(api.fetch)),
        requestJira: (0, exports.wrapRequestProduct)(wrapFetch(api.requestJira)),
        requestConfluence: (0, exports.wrapRequestProduct)(wrapFetch(api.requestConfluence)),
        requestBitbucket: (0, exports.wrapRequestProduct)(wrapFetch(api.requestBitbucket)),
        asUser: () => ({
            requestJira: (0, exports.wrapRequestProduct)(wrapFetch(api.asUser().requestJira)),
            requestConfluence: (0, exports.wrapRequestProduct)(wrapFetch(api.asUser().requestConfluence)),
            requestBitbucket: (0, exports.wrapRequestProduct)(wrapFetch(api.asUser().requestBitbucket)),
            requestGraph: (0, exports.wrapRequestGraph)(wrapFetch(api.asUser().requestGraph)),
            requestConnectedData: (0, exports.wrapRequestConnectedData)(wrapFetch(api.asUser().requestConnectedData)),
            withProvider: (provider, remoteName, tokenId) => {
                const { hasCredentials, requestCredentials, listCredentials, fetch: withProviderFetch, listAccounts, getAccount, asAccount } = api.asUser().withProvider(provider, remoteName, tokenId);
                const wrappedRequestRemote = (0, exports.wrapWithRouteUnwrapper)(wrapFetch(withProviderFetch));
                return {
                    hasCredentials,
                    requestCredentials,
                    listCredentials,
                    fetch: wrappedRequestRemote,
                    listAccounts,
                    getAccount,
                    asAccount
                };
            }
        }),
        asApp: () => ({
            requestJira: (0, exports.wrapRequestProduct)(wrapFetch(api.asApp().requestJira)),
            requestConfluence: (0, exports.wrapRequestProduct)(wrapFetch(api.asApp().requestConfluence)),
            requestGraph: (0, exports.wrapRequestGraph)(wrapFetch(api.asApp().requestGraph)),
            requestBitbucket: (0, exports.wrapRequestProduct)(wrapFetch(api.asApp().requestBitbucket)),
            requestConnectedData: (0, exports.wrapRequestConnectedData)(wrapFetch(api.asApp().requestConnectedData))
        })
    };
};
exports.wrapFetchApiMethods = wrapFetchApiMethods;
