import type { Metrics, Tags } from '@forge/util/packages/metrics-interface';
import { AppAri, EnvironmentAri, InstallationAri } from './ari';
export declare type ProxyInfo = {
    token: string;
    url: string;
    host?: string;
};
export declare type ExternalAuthMetaDataAccount = {
    id: string;
    externalAccountId: string;
    displayName: string;
    scopes: string[];
    avatarUrl?: string;
};
export declare type ExternalAuthMetaData = {
    service: string;
    remotes: {
        baseUrl: string;
        key: string;
    }[];
    accounts: ExternalAuthMetaDataAccount[];
};
export declare type CapabilitySet = 'capabilityStandard' | 'capabilityAdvanced';
export declare type License = {
    isActive?: boolean;
    billingPeriod?: string;
    capabilitySet?: CapabilitySet;
    ccpEntitlementId?: string;
    ccpEntitlementSlug?: string;
    isEvaluation?: boolean;
    subscriptionEndDate?: string;
    supportEntitlementNumber?: string;
    trialEndDate?: string;
    type?: string;
};
export interface ContextAri {
    cloudId?: string;
    workspaceId?: string;
    toString: () => string;
}
export interface Installation {
    ari: InstallationAri;
    contexts: ContextAri[];
}
export declare type ForgeRuntime = {
    proxy: ProxyInfo;
    contextAri: string;
    container: {
        region?: string;
        runtime?: string;
        handler?: string;
        appCodeDir?: string;
    };
    appContext: {
        appId: string;
        appVersion: string;
        environmentId: string;
        environmentType: string;
        invocationId: string;
        installationId: string;
        functionKey: string;
        moduleType: string;
        moduleKey: string;
        license?: License;
        installation?: Installation;
    };
    aaid?: string;
    allowedEgress: string[];
    lambdaContext: {
        awsRequestId: string;
        getRemainingTimeInMillis(): number;
    };
    tracing: {
        traceId: string;
        spanId: string;
    };
    metrics: Metrics;
    rms?: {
        url: string;
        host: string;
    };
    kvs?: {
        url: string;
        host: string;
    };
    externalAuth?: ExternalAuthMetaData[];
    featureFlags: (flag: string) => boolean;
};
export declare type AppContext = {
    appAri: AppAri;
    appVersion: string;
    environmentAri: EnvironmentAri;
    environmentType: string;
    invocationId: string;
    invocationRemainingTimeInMillis(): number;
    installationAri: InstallationAri;
    moduleKey: string;
    license?: License;
    installation?: Installation;
};
export declare function __getRuntime(): ForgeRuntime;
export declare function getAppContext(): AppContext;
export declare function wrapInMetrics<Args extends unknown[], Ret>(name: string, fn: (...args: Args) => Promise<Ret>, { tags }?: {
    tags?: Tags;
}): (...args: Args) => Promise<Ret>;
export declare function bindInvocationContext<Func extends (...args: never[]) => unknown>(fn: Func): Func;
//# sourceMappingURL=runtime.d.ts.map