{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAA8C,QAAQ,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAExG,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAC;AAE5C,OAAO,EAAE,KAAK,EAAE,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,MAAM,cAAc,CAAC;AACtH,OAAO,EAEL,uBAAuB,EACvB,wBAAwB,EAEzB,MAAM,6BAA6B,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;AAC5C,OAAO,EAAE,cAAc,EAAE,MAAM,aAAa,CAAC;AAI7C,MAAM,WAAW,WAAW;IAC1B,IAAI,CAAC,EAAE,WAAW,GAAG,MAAM,GAAG,eAAe,CAAC;IAC9C,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACjC,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,eAAe,CAAC;IAC3B,MAAM,CAAC,EAAE,WAAW,CAAC;CACtB;AAED,MAAM,WAAW,OAAO;IACtB,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IAC9C,MAAM,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAC;IAC/B,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,MAAM,GAAG,IAAI,CAAC;IACrC,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,CAAC;IAC/B,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,IAAI,CAAC;IAE3C,OAAO,EAAE,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,KAAK,IAAI,KAAK,IAAI,CAAC;CACrE;AAED,MAAM,WAAW,QAAQ;IACvB,IAAI,EAAE,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;IACzB,IAAI,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC;IAC5B,WAAW,EAAE,MAAM,OAAO,CAAC,WAAW,CAAC,CAAC;IAExC,EAAE,EAAE,OAAO,CAAC;IACZ,MAAM,EAAE,MAAM,CAAC;IACf,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC;CAClB;AAED,oBAAY,WAAW,GAAG,QAAQ,CAAC;AACnC,oBAAY,WAAW,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,WAAW,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;AACpF,oBAAY,wBAAwB,GAAG,CAAC,GAAG,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,CAAC,EAAE,WAAW,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;AACzG,oBAAY,oBAAoB,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,WAAW,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;AAC5F,oBAAY,YAAY,GAAG,WAAW,CAAC;AAEvC,MAAM,WAAW,qBAAqB;IACpC,WAAW,EAAE,oBAAoB,CAAC;IAClC,iBAAiB,EAAE,oBAAoB,CAAC;IACxC,gBAAgB,EAAE,oBAAoB,CAAC;CACxC;AAED,MAAM,WAAW,mBAAmB;IAClC,YAAY,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,OAAO,CAAC,WAAW,CAAC,CAAC;CACvG;AAED,MAAM,WAAW,yBAAyB;IACxC,oBAAoB,EAAE,oBAAoB,CAAC;CAC5C;AAED,MAAM,WAAW,mBAAmB;IAClC,EAAE,EAAE,MAAM,CAAC;IACX,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,EAAE,CAAC;CAClB;AAED,MAAM,WAAW,0BAA0B;IACzC,cAAc,EAAE,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;IACxD,kBAAkB,EAAE,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;IAC5D,KAAK,EAAE,wBAAwB,CAAC;IAChC,UAAU,EAAE,MAAM,OAAO,CAAC,mBAAmB,GAAG,SAAS,CAAC,CAAC;CAC5D;AACD,MAAM,WAAW,wBAAyB,SAAQ,0BAA0B;IAC1E,eAAe,EAAE,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,YAAY,EAAE,MAAM,OAAO,CAAC,mBAAmB,EAAE,CAAC,CAAC;IACnD,SAAS,EAAE,CAAC,iBAAiB,EAAE,MAAM,KAAK,0BAA0B,CAAC;CACtE;AAED,MAAM,WAAW,gCAAgC;IAC/C,YAAY,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,KAAK,wBAAwB,CAAC;CACrG;AAED,MAAM,WAAW,cAAc;IAC7B,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;IACnC,GAAG,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACpG,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;CACxC;AAED,MAAM,WAAW,UAAW,SAAQ,cAAc,EAAE,QAAQ,EAAE,gBAAgB;CAAG;AAEjF,MAAM,WAAW,eAAgB,SAAQ,UAAU;IACjD,SAAS,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC;IACzC,SAAS,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACtD,YAAY,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;CAC9C;AAED,oBAAY,kBAAkB,GAAG,qBAAqB,GACpD,mBAAmB,GACnB,yBAAyB,GACzB,gCAAgC,CAAC;AAEnC,oBAAY,iBAAiB,GAAG,qBAAqB,GAAG,mBAAmB,GAAG,yBAAyB,CAAC;AAExG,MAAM,WAAW,QAAS,SAAQ,qBAAqB;IACrD,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,kBAAkB,CAAC;IAC5C,KAAK,IAAI,iBAAiB,CAAC;IAC3B,KAAK,EAAE,wBAAwB,CAAC;CACjC;AAED,MAAM,WAAW,QAAS,SAAQ,QAAQ;IACxC,YAAY,EAAE,OAAO,YAAY,CAAC;CACnC;AAGD,QAAA,MAAM,MAAM,YAVM,MAAM,KAAG,kBAUG,CAAC;AAC/B,QAAA,MAAM,KAAK,QAVA,iBAUiB,CAAC;AAC7B,QAAA,MAAM,KAAK,0BAAiB,CAAC;AAC7B,QAAA,MAAM,WAAW,sBAAuB,CAAC;AACzC,QAAA,MAAM,iBAAiB,sBAA6B,CAAC;AACrD,QAAA,MAAM,gBAAgB,sBAA4B,CAAC;AAEnD,QAAA,MAAM,OAAO,EAAE,eAAqG,CAAC;AAErH,QAAA,MAAM,GAAG,EAAE,QAGV,CAAC;AAEF,eAAO,MAAM,OAAO;;CAEnB,CAAC;AAEF,eAAe,GAAG,CAAC;AACnB,OAAO,EACL,MAAM,EACN,KAAK,EACL,SAAS,EACT,KAAK,EACL,WAAW,EACX,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,OAAO,EACP,UAAU,EACV,iBAAiB,EACjB,kBAAkB,EAClB,gBAAgB,EAChB,iBAAiB,EACjB,uBAAuB,EACvB,wBAAwB,EACxB,cAAc,EACf,CAAC;AAKF,eAAO,MAAM,0BAA0B,EAAE,MAAM,WAA2C,CAAC;AAE3F,OAAO,EACL,YAAY,EACZ,QAAQ,EACR,gBAAgB,EAChB,SAAS,EACT,UAAU,EACV,SAAS,EACT,MAAM,EACN,KAAK,EACL,cAAc,EACd,eAAe,EACf,UAAU,EACV,eAAe,EACf,gBAAgB,EAChB,SAAS,EACV,MAAM,gBAAgB,CAAC;AAExB,OAAO,EAAE,KAAK,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,KAAK,EAAE,MAAM,WAAW,CAAC;AAChF,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,qBAAqB,EAAE,MAAM,eAAe,CAAC;AAC7G,OAAO,EACL,wBAAwB,EACxB,+BAA+B,EAC/B,iBAAiB,EACjB,UAAU,EACV,+BAA+B,EAC/B,8BAA8B,EAC9B,6BAA6B,EAC7B,eAAe,EACf,8BAA8B,EAC9B,SAAS,EACT,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,eAAe,EAChB,MAAM,cAAc,CAAC;AAEtB,OAAO,EAAE,IAAI,EAAE,KAAK,mBAAmB,EAAE,MAAM,YAAY,CAAC"}