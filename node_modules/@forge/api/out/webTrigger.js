"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.webTrigger = void 0;
const runtime_1 = require("./api/runtime");
const fetch_and_storage_1 = require("./runtime/fetch-and-storage");
const proxyGetWebTriggerURL = (0, runtime_1.wrapInMetrics)('api.getWebTriggerUrl', async (webTriggerModuleKey, forceCreate) => {
    const runtime = (0, runtime_1.__getRuntime)();
    const response = await (0, fetch_and_storage_1.__requestAtlassianAsApp)('/graphql', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            query: `
            mutation forge_app_createWebTriggerUrl($input: WebTriggerUrlInput!, $forceCreate: Boolean) {
              createWebTriggerUrl(input: $input, forceCreate: $forceCreate) {
                url
              }
            }
          `,
            variables: {
                input: {
                    appId: runtime.appContext.appId,
                    envId: runtime.appContext.environmentId,
                    triggerKey: webTriggerModuleKey,
                    contextId: runtime.contextAri
                },
                forceCreate
            }
        })
    });
    if (!response.ok) {
        throw new Error(`Internal error occurred: Failed to get web trigger URL: ${response.statusText}.`);
    }
    const responseBody = (await response.json());
    if (!responseBody?.data?.createWebTriggerUrl?.url) {
        throw new Error(`Internal error occurred: Failed to get web trigger URL.`);
    }
    return responseBody.data.createWebTriggerUrl.url;
});
const proxyDeleteWebTriggerURL = (0, runtime_1.wrapInMetrics)('api.deleteWebTriggerUrl', async (webTriggerUrl) => {
    const callDelete = async (webTriggerUrlId) => {
        const response = await (0, fetch_and_storage_1.__requestAtlassianAsApp)('/graphql', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                query: `
            mutation forge_app_deleteWebTriggerUrl($id: ID!) {
              deleteWebTriggerUrl(id: $id) {
                success
                message
              }
            }
          `,
                variables: {
                    id: webTriggerUrlId
                }
            })
        });
        if (!response.ok) {
            throw new Error(`Internal error occurred: Failed to delete web trigger URL: ${response.statusText}.`);
        }
        const responseBody = (await response.json());
        if (!responseBody?.data?.deleteWebTriggerUrl?.success) {
            const errorText = responseBody?.data?.deleteWebTriggerUrl?.message || 'unknown error';
            throw new Error(`Internal error occurred: Failed to delete web trigger URL: ${errorText}`);
        }
    };
    const urlIds = await exports.webTrigger.getUrlIds(webTriggerUrl);
    for (const urlId of urlIds) {
        await callDelete(urlId);
    }
});
const proxyGetWebTriggerUrlIds = (0, runtime_1.wrapInMetrics)('api.getWebTriggerUrlIds', async (webTriggerUrl) => {
    const runtime = (0, runtime_1.__getRuntime)();
    const response = await (0, fetch_and_storage_1.__requestAtlassianAsApp)('/graphql', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            query: `
            query forge_app_webTriggerUrlsByAppContext($appId: ID!, $envId: ID!, $contextId: ID!) {
              webTriggerUrlsByAppContext(appId: $appId, envId: $envId, contextId: $contextId) {
                id
                url
              }
            }
          `,
            variables: {
                appId: runtime.appContext.appId,
                envId: runtime.appContext.environmentId,
                contextId: runtime.contextAri
            }
        })
    });
    if (!response.ok) {
        throw new Error(`Internal error occurred: Failed to get web trigger URLs: ${response.statusText}.`);
    }
    const responseBody = (await response.json());
    if (!responseBody?.data?.webTriggerUrlsByAppContext || responseBody.data.webTriggerUrlsByAppContext.length == 0) {
        throw new Error('Internal error occurred: No web trigger URLs found');
    }
    const result = responseBody.data.webTriggerUrlsByAppContext
        .filter((webTriggerResult) => webTriggerResult.url == webTriggerUrl)
        .map((webTriggerResult) => webTriggerResult.id);
    if (!result || result.length == 0) {
        throw new Error('Internal error occurred: Web trigger URL matching URL not found');
    }
    return result;
});
exports.webTrigger = {
    getUrl: async (webTriggerModuleKey, forceCreate = false) => (global.api?.webTrigger?.getUrl ?? proxyGetWebTriggerURL)(webTriggerModuleKey, forceCreate),
    deleteUrl: async (webTriggerUrl) => (global.api?.webTrigger?.deleteUrl ?? proxyDeleteWebTriggerURL)(webTriggerUrl),
    getUrlIds: async (webTriggerUrl) => (global.api?.webTrigger?.getUrlIds ?? proxyGetWebTriggerUrlIds)(webTriggerUrl)
};
