"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.i18n = exports.isExpectedError = exports.isHostedCodeError = exports.isForgePlatformError = exports.FUNCTION_ERR = exports.HttpError = exports.InvalidWorkspaceRequestedError = exports.NotAllowedError = exports.RequestProductNotAllowedError = exports.ProductEndpointNotAllowedError = exports.ExternalEndpointNotAllowedError = exports.FetchError = exports.ProxyRequestError = exports.NeedsAuthenticationError = exports.bindInvocationContext = exports.__getRuntime = exports.getAppContext = exports.routeFromAbsolute = exports.assumeTrustedRoute = exports.route = exports.SortOrder = exports.FilterConditions = exports.WhereConditions = exports.startsWith = exports.createRequestStargateAsApp = exports.__fetchProduct = exports.__requestAtlassianAsUser = exports.__requestAtlassianAsApp = exports.webTrigger = exports.storage = exports.invokeRemote = exports.requestBitbucket = exports.requestConfluence = exports.requestJira = exports.fetch = exports.authorize = exports.asApp = exports.asUser = exports.privacy = void 0;
const storage_1 = require("@forge/storage");
const authorization_1 = require("./authorization");
Object.defineProperty(exports, "authorize", { enumerable: true, get: function () { return authorization_1.authorize; } });
const privacy_1 = require("./privacy");
const webTrigger_1 = require("./webTrigger");
Object.defineProperty(exports, "webTrigger", { enumerable: true, get: function () { return webTrigger_1.webTrigger; } });
const fetch_and_storage_1 = require("./runtime/fetch-and-storage");
Object.defineProperty(exports, "__requestAtlassianAsApp", { enumerable: true, get: function () { return fetch_and_storage_1.__requestAtlassianAsApp; } });
Object.defineProperty(exports, "__requestAtlassianAsUser", { enumerable: true, get: function () { return fetch_and_storage_1.__requestAtlassianAsUser; } });
const remote_1 = require("./api/remote");
Object.defineProperty(exports, "invokeRemote", { enumerable: true, get: function () { return remote_1.invokeRemote; } });
const fetch_1 = require("./api/fetch");
Object.defineProperty(exports, "__fetchProduct", { enumerable: true, get: function () { return fetch_1.__fetchProduct; } });
const fetchAPI = (0, fetch_and_storage_1.getFetchAPI)();
const asUser = fetchAPI.asUser;
exports.asUser = asUser;
const asApp = fetchAPI.asApp;
exports.asApp = asApp;
const fetch = fetchAPI.fetch;
exports.fetch = fetch;
const requestJira = fetchAPI.requestJira;
exports.requestJira = requestJira;
const requestConfluence = fetchAPI.requestConfluence;
exports.requestConfluence = requestConfluence;
const requestBitbucket = fetchAPI.requestBitbucket;
exports.requestBitbucket = requestBitbucket;
const storage = (0, storage_1.getStorageInstanceWithQuery)(new storage_1.GlobalStorage(fetch_and_storage_1.__requestAtlassianAsApp, fetch_and_storage_1.getMetrics));
exports.storage = storage;
const API = {
    ...fetchAPI,
    invokeRemote: remote_1.invokeRemote
};
exports.privacy = {
    reportPersonalData: (0, privacy_1.createReportPersonalData)(fetch_and_storage_1.__requestAtlassianAsApp)
};
exports.default = API;
const createRequestStargateAsApp = () => fetch_and_storage_1.__requestAtlassianAsApp;
exports.createRequestStargateAsApp = createRequestStargateAsApp;
var storage_2 = require("@forge/storage");
Object.defineProperty(exports, "startsWith", { enumerable: true, get: function () { return storage_2.startsWith; } });
Object.defineProperty(exports, "WhereConditions", { enumerable: true, get: function () { return storage_2.WhereConditions; } });
Object.defineProperty(exports, "FilterConditions", { enumerable: true, get: function () { return storage_2.FilterConditions; } });
Object.defineProperty(exports, "SortOrder", { enumerable: true, get: function () { return storage_2.SortOrder; } });
var safeUrl_1 = require("./safeUrl");
Object.defineProperty(exports, "route", { enumerable: true, get: function () { return safeUrl_1.route; } });
Object.defineProperty(exports, "assumeTrustedRoute", { enumerable: true, get: function () { return safeUrl_1.assumeTrustedRoute; } });
Object.defineProperty(exports, "routeFromAbsolute", { enumerable: true, get: function () { return safeUrl_1.routeFromAbsolute; } });
var runtime_1 = require("./api/runtime");
Object.defineProperty(exports, "getAppContext", { enumerable: true, get: function () { return runtime_1.getAppContext; } });
Object.defineProperty(exports, "__getRuntime", { enumerable: true, get: function () { return runtime_1.__getRuntime; } });
Object.defineProperty(exports, "bindInvocationContext", { enumerable: true, get: function () { return runtime_1.bindInvocationContext; } });
var errors_1 = require("./api/errors");
Object.defineProperty(exports, "NeedsAuthenticationError", { enumerable: true, get: function () { return errors_1.NeedsAuthenticationError; } });
Object.defineProperty(exports, "ProxyRequestError", { enumerable: true, get: function () { return errors_1.ProxyRequestError; } });
Object.defineProperty(exports, "FetchError", { enumerable: true, get: function () { return errors_1.FetchError; } });
Object.defineProperty(exports, "ExternalEndpointNotAllowedError", { enumerable: true, get: function () { return errors_1.ExternalEndpointNotAllowedError; } });
Object.defineProperty(exports, "ProductEndpointNotAllowedError", { enumerable: true, get: function () { return errors_1.ProductEndpointNotAllowedError; } });
Object.defineProperty(exports, "RequestProductNotAllowedError", { enumerable: true, get: function () { return errors_1.RequestProductNotAllowedError; } });
Object.defineProperty(exports, "NotAllowedError", { enumerable: true, get: function () { return errors_1.NotAllowedError; } });
Object.defineProperty(exports, "InvalidWorkspaceRequestedError", { enumerable: true, get: function () { return errors_1.InvalidWorkspaceRequestedError; } });
Object.defineProperty(exports, "HttpError", { enumerable: true, get: function () { return errors_1.HttpError; } });
Object.defineProperty(exports, "FUNCTION_ERR", { enumerable: true, get: function () { return errors_1.FUNCTION_ERR; } });
Object.defineProperty(exports, "isForgePlatformError", { enumerable: true, get: function () { return errors_1.isForgePlatformError; } });
Object.defineProperty(exports, "isHostedCodeError", { enumerable: true, get: function () { return errors_1.isHostedCodeError; } });
Object.defineProperty(exports, "isExpectedError", { enumerable: true, get: function () { return errors_1.isExpectedError; } });
var i18n_1 = require("./api/i18n");
Object.defineProperty(exports, "i18n", { enumerable: true, get: function () { return i18n_1.i18n; } });
