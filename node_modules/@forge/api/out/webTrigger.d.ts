interface WebTriggerAPI {
    getUrl: (webTriggerModuleKey: string, forceCreate?: boolean) => Promise<string>;
    deleteUrl: (webTriggerUrl: string) => Promise<void>;
    getUrlIds: (webTriggerUrl: string) => Promise<string[]>;
}
export declare const webTrigger: WebTriggerAPI;
/** @experimental */
export interface WebTriggerResponse {
    statusCode: number;
    statusText?: string;
    body?: string;
    headers?: {
        [key: string]: string[];
    };
}
/** @experimental */
export interface WebTriggerContext {
    installContext: `ari:${string}`;
}
/** @experimental */
export declare type WebTriggerMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
/** @experimental */
export interface WebTriggerRequest {
    method: WebTriggerMethod;
    body: string;
    path: string;
    headers: Record<string, string[]>;
    queryParameters: Record<string, string[]>;
}
export {};
//# sourceMappingURL=webTrigger.d.ts.map