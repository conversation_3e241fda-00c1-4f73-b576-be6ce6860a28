# Forge API

API methods exposed during runtime in Forge apps. These include methods to simplify common actions such as making 
REST requests to Atlassian products.

## Requirements

See [Set up Forge](https://developer.atlassian.com/platform/forge/set-up-forge/) for details of the software required to develop Forge apps.

## Usage

In your Forge app, include the following:

```

import api from '@forge/api';

```

See [Runtime API reference](https://developer.atlassian.com/platform/forge/runtime-api-reference/) for details of the methods available and how to use them.

## Support

See [Get help](https://developer.atlassian.com/platform/forge/get-help/) for how to get help and provide feedback.
