{"name": "@forge/api", "version": "5.3.0", "description": "Forge API methods", "author": "Atlassian", "license": "SEE LICENSE IN LICENSE.txt", "main": "out/index.js", "types": "out/index.d.ts", "scripts": {"build": "yarn run clean && yarn run compile", "clean": "rm -rf ./out && rm -f tsconfig.tsbuildinfo", "compile": "tsc -b -v", "postbuild": "yarn bolt w forge-scripts preserve-deprecated-tags ../forge-api"}, "devDependencies": {"@forge/runtime": "5.10.8", "@types/node": "14.18.63", "@types/node-fetch": "^2.6.11", "expect-type": "^0.17.3", "express": "^4.18.3", "jest-matcher-specific-error": "^1.0.0", "nock": "13.5.4"}, "dependencies": {"@forge/auth": "0.0.8", "@forge/egress": "1.4.1", "@forge/i18n": "0.0.6", "@forge/storage": "1.8.1", "@forge/util": "1.4.9", "headers-utils": "^3.0.2"}, "publishConfig": {"registry": "https://packages.atlassian.com/api/npm/npm-public/"}}