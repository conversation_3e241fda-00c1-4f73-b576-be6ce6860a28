# @forge/api

## 5.3.0

### Minor Changes

- 908388a: Enable user impersonation via allowing to pass a user ID in `asUser` method in `fetch`.

## 5.3.0-next.0

### Minor Changes

- 908388a: Enable user impersonation via allowing to pass a user ID in `asUser` method in `fetch`.

## 5.2.1

### Patch Changes

- 984c454: Forcing patch bump to update internal registry
- Updated dependencies [984c454]
  - @forge/auth@0.0.8
  - @forge/egress@1.4.1
  - @forge/i18n@0.0.6
  - @forge/storage@1.8.1
  - @forge/util@1.4.9

## 5.2.1-next.0

### Patch Changes

- 984c454: Forcing patch bump to update internal registry
- Updated dependencies [984c454]
  - @forge/auth@0.0.8-next.0
  - @forge/egress@1.4.1-next.0
  - @forge/i18n@0.0.6-next.0
  - @forge/storage@1.8.1-next.0
  - @forge/util@1.4.9-next.0

## 5.2.0

### Minor Changes

- 04ab98e: Add i18n module to enable forge resolvers to perform translations

### Patch Changes

- Updated dependencies [336f74f]
- Updated dependencies [e404ba0]
  - @forge/i18n@0.0.5

## 5.2.0-next.1

### Patch Changes

- Updated dependencies [e404ba0]
  - @forge/i18n@0.0.5-next.1

## 5.2.0-next.0

### Minor Changes

- 04ab98e: Add i18n module to enable forge resolvers to perform translations

### Patch Changes

- Updated dependencies [336f74f]
  - @forge/i18n@0.0.5-next.0

## 5.1.1

### Patch Changes

- 576da26: Rename products with contexts
- Updated dependencies [4e61715]
  - @forge/storage@1.8.0

## 5.1.1-next.1

### Patch Changes

- Updated dependencies [4e61715]
  - @forge/storage@1.8.0-next.0

## 5.1.1-next.0

### Patch Changes

- 576da26: Rename products with contexts

## 5.1.0

### Minor Changes

- fa82e2a: Add installation to AppContext
- a72008e: add graph value in fetch types
- 2086b3d: Remove node-fetch as a dependency

### Patch Changes

- fa77f8d: Revert the package registry change for the package
- 503e60e: Add publish config
- 55d56f7: update typing to add support to kvs config
- Updated dependencies [f2c2405]
- Updated dependencies [c25288c]
- Updated dependencies [fa77f8d]
- Updated dependencies [89c96bf]
- Updated dependencies [503e60e]
- Updated dependencies [3302cc2]
- Updated dependencies [2086b3d]
  - @forge/egress@1.4.0
  - @forge/storage@1.7.3
  - @forge/auth@0.0.7
  - @forge/util@1.4.8

## 5.1.0-next.9

### Patch Changes

- fa77f8d: Revert the package registry change for the package
- Updated dependencies [fa77f8d]
  - @forge/storage@1.7.3-next.1
  - @forge/egress@1.4.0-next.5
  - @forge/auth@0.0.7-next.1
  - @forge/util@1.4.8-next.2

## 5.1.0-next.8

### Patch Changes

- 503e60e: Add publish config
- Updated dependencies [503e60e]
  - @forge/storage@1.7.3-next.0
  - @forge/egress@1.4.0-next.4
  - @forge/auth@0.0.7-next.0
  - @forge/util@1.4.8-next.1

## 5.1.0-next.7

### Minor Changes

- 2086b3d: Remove node-fetch as a dependency

### Patch Changes

- Updated dependencies [2086b3d]
  - @forge/egress@1.4.0-next.3

## 5.1.0-next.6

### Patch Changes

- Updated dependencies [f2c2405]
  - @forge/egress@1.4.0-next.2

## 5.1.0-next.5

### Minor Changes

- fa82e2a: Add installation to AppContext

## 5.1.0-next.4

### Patch Changes

- Updated dependencies [89c96bf]
  - @forge/egress@1.4.0-next.1

## 5.1.0-next.3

### Patch Changes

- Updated dependencies [c25288c]
  - @forge/egress@1.4.0-next.0

## 5.1.0-next.2

### Minor Changes

- a72008e: add graph value in fetch types

## 5.0.2-next.1

### Patch Changes

- Updated dependencies [3302cc2]
  - @forge/util@1.4.8-next.0

## 5.0.2-next.0

### Patch Changes

- 55d56f7: update typing to add support to kvs config

## 5.0.1

### Patch Changes

- a5e7647: Update license for Forge public packages
- Updated dependencies [f8e2503]
- Updated dependencies [be59c90]
- Updated dependencies [eb52ac8]
- Updated dependencies [a5e7647]
  - @forge/storage@1.7.2
  - @forge/util@1.4.7
  - @forge/egress@1.3.1
  - @forge/auth@0.0.6

## 5.0.1-next.3

### Patch Changes

- a5e7647: Update license for Forge public packages
- Updated dependencies [a5e7647]
  - @forge/storage@1.7.2-next.1
  - @forge/egress@1.3.1-next.0
  - @forge/auth@0.0.6-next.0
  - @forge/util@1.4.7-next.2

## 5.0.1-next.2

### Patch Changes

- Updated dependencies [eb52ac8]
  - @forge/util@1.4.7-next.1

## 5.0.1-next.1

### Patch Changes

- Updated dependencies [be59c90]
  - @forge/util@1.4.7-next.0

## 5.0.1-next.0

### Patch Changes

- Updated dependencies [f8e2503]
  - @forge/storage@1.7.2-next.0

## 5.0.0

### Major Changes

- a5a6cfb: Replacing node-fetch client with Undici for HTTP requests

### Patch Changes

- 05f0f1a: Stub global fetch if it is not available when trying to construct APIs for the Node runtime
- Updated dependencies [a5a6cfb]
  - @forge/storage@1.7.1

## 5.0.0-next.1

### Patch Changes

- 05f0f1a: Stub global fetch if it is not available when trying to construct APIs for the Node runtime

## 5.0.0-next.0

### Major Changes

- a5a6cfb: Replacing node-fetch client with Undici for HTTP requests

### Patch Changes

- Updated dependencies [a5a6cfb]
  - @forge/storage@1.7.1-next.0

## 4.3.0

### Minor Changes

- e23f188: Remove contextAri from payload

### Patch Changes

- 7e6baa1: Link to app context documentation
- Updated dependencies [e23f188]
  - @forge/storage@1.7.0

## 4.3.0-next.1

### Minor Changes

- e23f188: Remove contextAri from payload

### Patch Changes

- Updated dependencies [e23f188]
  - @forge/storage@1.7.0-next.0

## 4.2.1-next.0

### Patch Changes

- 7e6baa1: Link to app context documentation

## 4.2.0

### Minor Changes

- c0f4d43: Get remaining invocation time for the function

### Patch Changes

- Updated dependencies [2e198f0]
- Updated dependencies [26050db]
  - @forge/egress@1.3.0
  - @forge/util@1.4.6

## 4.2.0-next.2

### Patch Changes

- Updated dependencies [26050db]
  - @forge/util@1.4.6-next.0

## 4.2.0-next.1

### Patch Changes

- Updated dependencies [2e198f0]
  - @forge/egress@1.3.0-next.0

## 4.2.0-next.0

### Minor Changes

- c0f4d43: Get remaining invocation time for the function

## 4.1.2

### Patch Changes

- 01f334b: preparing for upcoming object store

## 4.1.2-next.0

### Patch Changes

- 01f334b: preparing for upcoming object store

## 4.1.1

### Patch Changes

- Updated dependencies [ffa1464]
  - @forge/util@1.4.5

## 4.1.1-next.0

### Patch Changes

- Updated dependencies [ffa1464]
  - @forge/util@1.4.5-next.0

## 4.1.0

### Minor Changes

- a989ae4: Add bindInvocationContext api to attach runtime metadata to current async context

## 4.1.0-next.0

### Minor Changes

- a989ae4: Add bindInvocationContext api to attach runtime metadata to current async context

## 4.0.0

### Major Changes

- 7e05319: The deprecated Properties API has been removed.

  This api was deprected here: https://developer.atlassian.com/platform/forge/changelog/#CHANGE-1231

### Minor Changes

- a17ae69: Calls to storage api now capture success rates and latency metrics.

### Patch Changes

- e069ff4: Fixed bug in metric collection in sandbox environment for global storage api
- Updated dependencies [a17ae69]
- Updated dependencies [e069ff4]
  - @forge/storage@1.6.0

## 4.0.0-next.2

### Patch Changes

- e069ff4: Fixed bug in metric collection in sandbox environment for global storage api
- Updated dependencies [e069ff4]
  - @forge/storage@1.6.0-next.1

## 4.0.0-next.1

### Major Changes

- 7e05319: The deprecated Properties API has been removed.

  This api was deprected here: https://developer.atlassian.com/platform/forge/changelog/#CHANGE-1231

## 3.10.0-next.0

### Minor Changes

- a17ae69: Calls to storage api now capture success rates and latency metrics.

### Patch Changes

- Updated dependencies [a17ae69]
  - @forge/storage@1.6.0-next.0

## 3.9.2

### Patch Changes

- d3e9d37: Add default content-type as JSON in @forge/api product requests
- fbb27ea: add WebTrigger request and response types
- 0f75e93: Forge SQL SDK added

## 3.9.2-next.2

### Patch Changes

- 0f75e93: Forge SQL SDK added

## 3.9.2-next.1

### Patch Changes

- fbb27ea: add WebTrigger request and response types

## 3.9.2-next.0

### Patch Changes

- d3e9d37: Add default content-type as JSON in @forge/api product requests

## 3.9.1

### Patch Changes

- 6c9b381: Update comment referent to node-runtime (no code change)

## 3.9.1-next.0

### Patch Changes

- 6c9b381: Update comment referent to node-runtime (no code change)

## 3.9.0

### Minor Changes

- 1eaeb60: Include app license information in the getAppContext Api

### Patch Changes

- 59f7240: Remove unsupported provider type 'none' in getRequestStargate function

## 3.9.0-next.1

### Patch Changes

- 59f7240: Remove unsupported provider type 'none' in getRequestStargate function

## 3.9.0-next.0

### Minor Changes

- 1eaeb60: Include app license information in the getAppContext Api

## 3.8.1

### Patch Changes

- d4f9acd: Update internal implementation of Installation ARI
- Updated dependencies [99fb753]
  - @forge/util@1.4.4

## 3.8.1-next.1

### Patch Changes

- Updated dependencies [99fb753]
  - @forge/util@1.4.4-next.0

## 3.8.1-next.0

### Patch Changes

- d4f9acd: Update internal implementation of Installation ARI

## 3.8.0

### Minor Changes

- 567b79a: Add invokeRemote to @forge/api

## 3.8.0-next.0

### Minor Changes

- 567b79a: Add invokeRemote to @forge/api

## 3.7.1

### Patch Changes

- 6b04ab2: Fix \_\_requestAtlassianAsUser to use asUser on sandbox runtime

## 3.7.1-next.0

### Patch Changes

- 6b04ab2: Fix \_\_requestAtlassianAsUser to use asUser on sandbox runtime

## 3.7.0

### Minor Changes

- 2cd49c2: Add \_\_requestAtlassian methods

## 3.7.0-next.0

### Minor Changes

- 2cd49c2: Add \_\_requestAtlassian methods

## 3.6.0

### Minor Changes

- 1588c76: Embed metrics for external auth method calls in Node Runtime

## 3.6.0-next.0

### Minor Changes

- 1588c76: Embed metrics for external auth method calls in Node Runtime

## 3.5.0

### Minor Changes

- 94956a5: Avoid logging NEEDS_AUTHENTICATION_ERR when it is thrown from requestCredentials()

### Patch Changes

- f8a4714: Use InMemoryMetrics from the Node monorepo
- Updated dependencies [f8a4714]
  - @forge/util@1.4.3

## 3.5.0-next.1

### Minor Changes

- 94956a5: Avoid logging NEEDS_AUTHENTICATION_ERR when it is thrown from requestCredentials()

## 3.4.1-next.0

### Patch Changes

- f8a4714: Use InMemoryMetrics from the Node monorepo
- Updated dependencies [f8a4714]
  - @forge/util@1.4.3-next.0

## 3.4.0

### Minor Changes

- 4d149f1: Support absolute paths for withProvider fetch for the Node runtime

## 3.4.0-next.0

### Minor Changes

- 4d149f1: Support absolute paths for withProvider fetch for the Node runtime

## 3.3.1

### Patch Changes

- da345ba: Fix custom authentication header for first party api request
- 67590d1: Update type used by Node runtime

## 3.3.1-next.1

### Patch Changes

- 67590d1: Update type used by Node runtime

## 3.3.1-next.0

### Patch Changes

- da345ba: Fix custom authentication header for first party api request

## 3.3.0

### Minor Changes

- 119eb24: Implement external auth API enhancements for Node runtime

### Patch Changes

- fc5ea37: Bumping dependencies via Renovate:

  - nock

- Updated dependencies [d76d95c]
  - @forge/util@1.4.2

## 3.3.0-next.2

### Patch Changes

- Updated dependencies [d76d95c]
  - @forge/util@1.4.2-next.0

## 3.3.0-next.1

### Patch Changes

- fc5ea37: Bumping dependencies via Renovate:

  - nock

## 3.3.0-next.0

### Minor Changes

- 119eb24: Implement external auth API enhancements for Node runtime

## 3.2.0

### Minor Changes

- e343baf: Add external auth info to metadata for invocations
- d36502b: This change extends existing methods on external auth providers. We are now providing account details including: externalAccountId, displayName, avatarUrl, granted scopes.
  Moreover, if end user authenticates multiple accounts, it is possible to select what account to use.
- ea39472: Implement External auth APIs for Node runtime

### Patch Changes

- 32fa518: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [5b82e7f]
- Updated dependencies [882be11]
- Updated dependencies [32fa518]
  - @forge/util@1.4.1
  - @forge/egress@1.2.13
  - @forge/storage@1.5.15

## 3.2.0-next.5

### Minor Changes

- ea39472: Implement External auth APIs for Node runtime

## 3.2.0-next.4

### Minor Changes

- e343baf: Add external auth info to metadata for invocations

## 3.2.0-next.3

### Patch Changes

- Updated dependencies [5b82e7f]
  - @forge/util@1.4.1-next.0

## 3.2.0-next.2

### Patch Changes

- Updated dependencies [882be11]
  - @forge/egress@1.2.13-next.0

## 3.2.0-next.1

### Minor Changes

- d36502b: This change extends existing methods on external auth providers. We are now providing account details including: externalAccountId, displayName, avatarUrl, granted scopes.
  Moreover, if end user authenticates multiple accounts, it is possible to select what account to use.

## 3.1.1-next.0

### Patch Changes

- 32fa518: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [32fa518]
  - @forge/storage@1.5.15-next.0

## 3.1.0

### Minor Changes

- 0c0cacd: requestCredentials for partial scopes

### Patch Changes

- 6fadf22: Bumping dependencies via Renovate:

  - nock

## 3.1.0-next.1

### Patch Changes

- 6fadf22: Bumping dependencies via Renovate:

  - nock

## 3.1.0-next.0

### Minor Changes

- 0c0cacd: requestCredentials for partial scopes

## 3.0.0

### Major Changes

- b3245a7: - Add environmentType, invocationId, installationId in runtime V2 context
  - Rename getRuntime to \_\_getRuntime in @forge/api
  - Add new method getAppContext in @forge/api to retrieve app context data
  - Replace @atlassian/cs-ari package with @atlassian/ari

### Minor Changes

- ace184d: Add requestConnectedData() to call Teamwork Graph APIs
- b04283b: Add serialize function for InstallationAri
- f3ca9bf: Update export type for InstallationAri and EnvironmentAri

### Patch Changes

- 73e1aac: Bumping dependencies via Renovate:

  - nock

- 579c6a2: send metrics with the same name but different tags separately
- 16a7cf5: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [b3245a7]
- Updated dependencies [c714732]
- Updated dependencies [16a7cf5]
  - @forge/util@1.4.0
  - @forge/egress@1.2.12
  - @forge/storage@1.5.14

## 3.0.0-next.7

### Patch Changes

- 73e1aac: Bumping dependencies via Renovate:

  - nock

## 3.0.0-next.6

### Patch Changes

- Updated dependencies [c714732]
  - @forge/egress@1.2.12-next.0

## 3.0.0-next.5

### Minor Changes

- b04283b: Add serialize function for InstallationAri

## 3.0.0-next.4

### Patch Changes

- 16a7cf5: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [16a7cf5]
  - @forge/storage@1.5.14-next.0

## 3.0.0-next.3

### Minor Changes

- f3ca9bf: Update export type for InstallationAri and EnvironmentAri

## 3.0.0-next.2

### Patch Changes

- 579c6a2: send metrics with the same name but different tags separately

## 3.0.0-next.1

### Major Changes

- b3245a7: - Add environmentType, invocationId, installationId in runtime V2 context
  - Rename getRuntime to \_\_getRuntime in @forge/api
  - Add new method getAppContext in @forge/api to retrieve app context data
  - Replace @atlassian/cs-ari package with @atlassian/ari

### Patch Changes

- Updated dependencies [b3245a7]
  - @forge/util@1.4.0-next.0

## 2.23.0-next.0

### Minor Changes

- ace184d: Add requestConnectedData() to call Teamwork Graph APIs

## 2.22.1

### Patch Changes

- Updated dependencies [b45f058]
  - @forge/egress@1.2.11

## 2.22.1-next.0

### Patch Changes

- Updated dependencies [b45f058]
  - @forge/egress@1.2.11-next.0

## 2.22.0

### Minor Changes

- 97bbfc10: Add deleteWebTriggerUrl mutation to forge-api for Web Triggers
- 1fd03c6d: Added getUrlId function to Web Trigger forge-api
- 52455789: Added deleteWebTriggerUrl mutation to forge-api and sandbox runtime

### Patch Changes

- 01eec989: Fix error where mutation was used instead of query
- Updated dependencies [2cf5ac83]
  - @forge/egress@1.2.10

## 2.22.0-next.2

### Patch Changes

- 01eec989: Fix error where mutation was used instead of query

## 2.22.0-next.1

### Minor Changes

- 97bbfc10: Add deleteWebTriggerUrl mutation to forge-api for Web Triggers
- 1fd03c6d: Added getUrlId function to Web Trigger forge-api
- 52455789: Added deleteWebTriggerUrl mutation to forge-api and sandbox runtime

## 2.21.1-next.0

### Patch Changes

- Updated dependencies [2cf5ac83]
  - @forge/egress@1.2.10-next.0

## 2.21.0

### Minor Changes

- da702738: Fixed force create mutation

### Patch Changes

- f30dfa79: Add optional forceCreate parameter to the webTrigger.getUrl function

## 2.21.0-next.1

### Minor Changes

- da702738: Fixed force create mutation

## 2.20.2-next.0

### Patch Changes

- f30dfa79: Add optional forceCreate parameter to the webTrigger.getUrl function

## 2.20.1

### Patch Changes

- 0cdbe736: Bumping dependencies via Renovate:

  - @types/node-fetch

- 859ce647: Bumping dependencies via Renovate:

  - nock

- 7149d164: Adds correct type for values expected in storage.set
- Updated dependencies [ea837416]
- Updated dependencies [3c3f7b1b]
- Updated dependencies [0cdbe736]
- Updated dependencies [7149d164]
  - @forge/egress@1.2.9
  - @forge/storage@1.5.13

## 2.20.1-next.4

### Patch Changes

- 0cdbe73: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [0cdbe73]
  - @forge/storage@1.5.13-next.1

## 2.20.1-next.3

### Patch Changes

- Updated dependencies [3c3f7b1]
  - @forge/egress@1.2.9-next.1

## 2.20.1-next.2

### Patch Changes

- Updated dependencies [ea83741]
  - @forge/egress@1.2.9-next.0

## 2.20.1-next.1

### Patch Changes

- 7149d16: Adds correct type for values expected in storage.set
- Updated dependencies [7149d16]
  - @forge/storage@1.5.13-next.0

## 2.20.1-next.0

### Patch Changes

- 859ce64: Bumping dependencies via Renovate:

  - nock

## 2.20.0

### Minor Changes

- 2764115: Prevent forward traversal in route

### Patch Changes

- 189720f: Notice of deprecation for properties API
- 8590410: Bumping dependencies via Renovate:

  - nock

- cd1dbce: Throw an error instead of silently failing on failed set calls
- a52c60e: Bumping dependencies via Renovate:

  - @types/node-fetch

- 5007b9f: Bumping dependencies via Renovate:

  - nock

- Updated dependencies [cde16c5]
- Updated dependencies [a52c60e]
- Updated dependencies [63aba96]
  - @forge/egress@1.2.8
  - @forge/storage@1.5.12

## 2.20.0-next.6

### Patch Changes

- cd1dbce: Throw an error instead of silently failing on failed set calls

## 2.20.0-next.5

### Patch Changes

- 5007b9f: Bumping dependencies via Renovate:

  - nock

## 2.20.0-next.4

### Patch Changes

- a52c60e: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [cde16c5]
- Updated dependencies [a52c60e]
  - @forge/egress@1.2.8-next.0
  - @forge/storage@1.5.12-next.1

## 2.20.0-next.3

### Patch Changes

- Updated dependencies [63aba96]
  - @forge/storage@1.5.12-next.0

## 2.20.0-next.2

### Patch Changes

- 189720f: Notice of deprecation for properties API

## 2.20.0-next.1

### Minor Changes

- 2764115: Prevent forward traversal in route

## 2.19.5-next.0

### Patch Changes

- 8590410: Bumping dependencies via Renovate:

  - nock

## 2.19.4

### Patch Changes

- a16f47e1: Added test cases for ensuring return type
- 85efdeba: Remove lambda default variables
- c4b3c64: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [a16f47e1]
- Updated dependencies [a3d620b]
- Updated dependencies [c4b3c64]
- Updated dependencies [3a6bae4]
- Updated dependencies [b5342a9]
  - @forge/storage@1.5.11
  - @forge/egress@1.2.7
  - @forge/util@1.3.3

## 2.19.4-next.5

### Patch Changes

- c4b3c64c: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [c4b3c64c]
  - @forge/storage@1.5.11-next.1

## 2.19.4-next.4

### Patch Changes

- Updated dependencies [a3d620b1]
  - @forge/egress@1.2.7-next.1

## 2.19.4-next.3

### Patch Changes

- Updated dependencies [3a6bae44]
  - @forge/egress@1.2.7-next.0

## 2.19.4-next.2

### Patch Changes

- Updated dependencies [b5342a9d]
  - @forge/util@1.3.3-next.0

## 2.19.4-next.1

### Patch Changes

- a16f47e1: Added test cases for ensuring return type
- Updated dependencies [a16f47e1]
  - @forge/storage@1.5.11-next.0

## 2.19.4-next.0

### Patch Changes

- 85efdeb: Remove lambda default variables

## 2.19.3

### Patch Changes

- dd43e2f: Fix trusted routes interpolated into larger routes
- c93b149: Bumping dependencies via Renovate:

  - nock

- Updated dependencies [863f7eb]
  - @forge/util@1.3.2

## 2.19.3-next.2

### Patch Changes

- c93b149: Bumping dependencies via Renovate:

  - nock

## 2.19.3-next.1

### Patch Changes

- dd43e2f: Fix trusted routes interpolated into larger routes

## 2.19.3-next.0

### Patch Changes

- Updated dependencies [863f7eb]
  - @forge/util@1.3.2-next.0

## 2.19.2

### Patch Changes

- ed2cbc8: Bumping dependencies via Renovate:

  - @types/node-fetch

- 9d50860: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [43121bc]
- Updated dependencies [0fbf747]
- Updated dependencies [ed2cbc8]
- Updated dependencies [9d50860]
  - @forge/egress@1.2.6
  - @forge/storage@1.5.10

## 2.19.2-next.3

### Patch Changes

- Updated dependencies [43121bc3]
  - @forge/egress@1.2.6-next.2

## 2.19.2-next.2

### Patch Changes

- Updated dependencies [0fbf747]
  - @forge/egress@1.2.6-next.1

## 2.19.2-next.1

### Patch Changes

- 9d50860c: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [9d50860c]
  - @forge/egress@1.2.6-next.0
  - @forge/storage@1.5.10-next.1

## 2.19.2-next.0

### Patch Changes

- ed2cbc8c: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [ed2cbc8c]
  - @forge/storage@1.5.10-next.0

## 2.19.1

### Patch Changes

- 641b0551: Bumping dependencies via Renovate:

  - @types/node-fetch

- efffc256: Bumping dependencies via Renovate:

  - @types/node

- c1c5fb59: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [641b0551]
- Updated dependencies [efffc256]
- Updated dependencies [c1c5fb59]
- Updated dependencies [6f4e2f01]
  - @forge/storage@1.5.9
  - @forge/egress@1.2.5

## 2.19.1-next.3

### Patch Changes

- Updated dependencies [6f4e2f01]
  - @forge/egress@1.2.5-next.2

## 2.19.1-next.2

### Patch Changes

- efffc256: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [efffc256]
  - @forge/egress@1.2.5-next.1
  - @forge/storage@1.5.9-next.2

## 2.19.1-next.1

### Patch Changes

- 641b0551: Bumping dependencies via Renovate:

  - @types/node-fetch

- Updated dependencies [641b0551]
  - @forge/storage@1.5.9-next.1

## 2.19.1-next.0

### Patch Changes

- c1c5fb5: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [c1c5fb5]
  - @forge/egress@1.2.5-next.0
  - @forge/storage@1.5.9-next.0

## 2.19.0

### Minor Changes

- 347359bf: Better protection against path traversal attacks in product requests

### Patch Changes

- ccc113ec: Bumping dependencies via Renovate:

  - @types/node

- e3260cf8: Bumping dependencies via Renovate:

  - @types/node

- 9f70463a: Bumping dependencies via Renovate:

  - node-fetch

- 770654be: Change supported Node versions to 18 and 20
- 9b9f58d3: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [ccc113ec]
- Updated dependencies [e3260cf8]
- Updated dependencies [9f70463a]
- Updated dependencies [bf0a343b]
- Updated dependencies [9b9f58d3]
  - @forge/egress@1.2.4
  - @forge/storage@1.5.8

## 2.19.0-next.6

### Patch Changes

- 770654be: Change supported Node versions to 18 and 20

## 2.19.0-next.5

### Patch Changes

- e3260cf: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [e3260cf]
  - @forge/egress@1.2.4-next.3
  - @forge/storage@1.5.8-next.3

## 2.19.0-next.4

### Minor Changes

- 347359b: Better protection against path traversal attacks in product requests

## 2.18.6-next.3

### Patch Changes

- ccc113ec: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [ccc113ec]
  - @forge/egress@1.2.4-next.2
  - @forge/storage@1.5.8-next.2

## 2.18.6-next.2

### Patch Changes

- 9f70463: Bumping dependencies via Renovate:

  - node-fetch

- Updated dependencies [9f70463]
  - @forge/storage@1.5.8-next.1

## 2.18.6-next.1

### Patch Changes

- Updated dependencies [bf0a343b]
  - @forge/egress@1.2.4-next.1

## 2.18.6-next.0

### Patch Changes

- 9b9f58d3: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [9b9f58d3]
  - @forge/egress@1.2.4-next.0
  - @forge/storage@1.5.8-next.0

## 2.18.5

### Patch Changes

- 035116d2: Bumping dependencies via Renovate:

  - node-fetch

- Updated dependencies [ba548d94]
- Updated dependencies [035116d2]
- Updated dependencies [439098ca]
  - @forge/auth@0.0.5
  - @forge/storage@1.5.7
  - @forge/egress@1.2.3

## 2.18.5-next.2

### Patch Changes

- Updated dependencies [439098ca]
  - @forge/egress@1.2.3-next.0

## 2.18.5-next.1

### Patch Changes

- Updated dependencies [ba548d9]
  - @forge/auth@0.0.5-next.0

## 2.18.5-next.0

### Patch Changes

- 035116d: Bumping dependencies via Renovate:

  - node-fetch

- Updated dependencies [035116d]
  - @forge/storage@1.5.7-next.0

## 2.18.4

### Patch Changes

- 4640d4fb: Bumping dependencies via Renovate:

  - @types/node

- 854ff5d: Fix Authorize API on Node runtime
- Updated dependencies [2cba32e]
- Updated dependencies [4640d4fb]
- Updated dependencies [854ff5d]
- Updated dependencies [f48e6d8]
  - @forge/storage@1.5.6
  - @forge/auth@0.0.4

## 2.18.4-next.3

### Patch Changes

- 854ff5d: Fix Authorize API on Node runtime
- Updated dependencies [854ff5d]
  - @forge/auth@0.0.4-next.1

## 2.18.4-next.2

### Patch Changes

- Updated dependencies [2cba32e]
  - @forge/storage@1.5.6-next.1

## 2.18.4-next.1

### Patch Changes

- Updated dependencies [f48e6d8]
  - @forge/auth@0.0.4-next.0

## 2.18.4-next.0

### Patch Changes

- 4640d4f: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [4640d4f]
  - @forge/storage@1.5.6-next.0

## 2.18.3

### Patch Changes

- 88be9538: Use HTTP polyfill to call proxy
- Updated dependencies [801ea11]
- Updated dependencies [8105c45]
- Updated dependencies [4999c4b]
- Updated dependencies [94b724a]
  - @forge/util@1.3.1
  - @forge/egress@1.2.2

## 2.18.3-next.4

### Patch Changes

- Updated dependencies [4999c4b]
  - @forge/util@1.3.1-next.2

## 2.18.3-next.3

### Patch Changes

- Updated dependencies [801ea11]
  - @forge/util@1.3.1-next.1

## 2.18.3-next.2

### Patch Changes

- Updated dependencies [94b724a]
  - @forge/util@1.3.1-next.0

## 2.18.3-next.1

### Patch Changes

- 88be9538: Use HTTP polyfill to call proxy

## 2.18.3-next.0

### Patch Changes

- Updated dependencies [8105c45]
  - @forge/egress@1.2.2-next.0

## 2.18.2

### Patch Changes

- a006df23: Bumping dependencies via Renovate:

  - @types/node

- 33e6ca1e: Bumping dependencies via Renovate:

  - node-fetch

- 607ae912: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [a006df23]
- Updated dependencies [33e6ca1e]
- Updated dependencies [732c136]
- Updated dependencies [607ae912]
- Updated dependencies [a1424a3]
- Updated dependencies [04271c2]
- Updated dependencies [6715b6a]
  - @forge/storage@1.5.5
  - @forge/auth@0.0.3
  - @forge/util@1.3.0

## 2.18.2-next.6

### Patch Changes

- Updated dependencies [a1424a3c]
  - @forge/util@1.3.0-next.2

## 2.18.2-next.5

### Patch Changes

- Updated dependencies [6715b6a1]
  - @forge/util@1.3.0-next.1

## 2.18.2-next.4

### Patch Changes

- Updated dependencies [04271c27]
  - @forge/util@1.2.4-next.0

## 2.18.2-next.3

### Patch Changes

- Updated dependencies [732c136a]
  - @forge/auth@0.0.3-next.0

## 2.18.2-next.2

### Patch Changes

- a006df23: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [a006df23]
  - @forge/storage@1.5.5-next.2

## 2.18.2-next.1

### Patch Changes

- 607ae91: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [607ae91]
  - @forge/storage@1.5.5-next.1

## 2.18.2-next.0

### Patch Changes

- 33e6ca1: Bumping dependencies via Renovate:

  - node-fetch

- Updated dependencies [33e6ca1]
  - @forge/storage@1.5.5-next.0

## 2.18.1

### Patch Changes

- e67ce5c: Fix TypeScript definitions relying on unexported types
- 270b46d: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [e67ce5c]
- Updated dependencies [270b46d]
  - @forge/auth@0.0.2
  - @forge/storage@1.5.4

## 2.18.1-next.1

### Patch Changes

- e67ce5c: Fix TypeScript definitions relying on unexported types
- Updated dependencies [e67ce5c]
  - @forge/auth@0.0.2-next.0

## 2.18.1-next.0

### Patch Changes

- 270b46da: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [270b46da]
  - @forge/storage@1.5.4-next.0

## 2.18.0

### Minor Changes

- 7cab7f9c: Handle redirect in nodejs runtime

## 2.18.0-next.0

### Minor Changes

- 7cab7f9c: Handle redirect in nodejs runtime

## 2.17.0

### Minor Changes

- 3edb8a19: Add tracing to experimental integration

### Patch Changes

- 5ba685b6: Add new success dimension to proxy fetch metrics on node runtime
- cf453494: Bumping dependencies via Renovate:

  - node-fetch
  - @types/node-fetch

- bb7b824d: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [cf453494]
- Updated dependencies [bb7b824d]
  - @forge/storage@1.5.3

## 2.17.0-next.3

### Patch Changes

- bb7b824: Bumping dependencies via Renovate:

  - @types/node

- Updated dependencies [bb7b824]
  - @forge/storage@1.5.3-next.1

## 2.17.0-next.2

### Patch Changes

- cf453494: Bumping dependencies via Renovate:

  - node-fetch
  - @types/node-fetch

- Updated dependencies [cf453494]
  - @forge/storage@1.5.3-next.0

## 2.17.0-next.1

### Patch Changes

- 5ba685b6: Add new success dimension to proxy fetch metrics on node runtime

## 2.17.0-next.0

### Minor Changes

- 3edb8a19: Add tracing to experimental integration

## 2.16.0

### Minor Changes

- f7f52209: Experimental integration (refactoring)
- c8e6b859: Experimental integration

### Patch Changes

- f877763a: Handle host option when proxying in the Node runtime

## 2.16.0-next.2

### Minor Changes

- f7f52209: Experimental integration (refactoring)

## 2.16.0-next.1

### Patch Changes

- f877763a: Handle host option when proxying in the Node runtime

## 2.16.0-next.0

### Minor Changes

- c8e6b85: Experimental integration

## 2.15.3

### Patch Changes

- 96f9f6d8: Handle URL and options passed together to HTTP polyfill
- Updated dependencies [96f9f6d8]
- Updated dependencies [f18ccd86]
  - @forge/storage@1.5.2
  - @forge/egress@1.2.1

## 2.15.3-next.1

### Patch Changes

- 96f9f6d8: Handle URL and options passed together to HTTP polyfill
- Updated dependencies [96f9f6d8]
  - @forge/storage@1.5.2-next.0

## 2.15.3-next.0

### Patch Changes

- Updated dependencies [f18ccd86]
  - @forge/egress@1.2.1-next.0

## 2.15.2

### Patch Changes

- 23a21ec: Bumping dependencies via Renovate:

  - portfinder

- 97b57e7: Bumping dependencies via Renovate:

  - unzipper
  - @types/unzipper

- Updated dependencies [23a21ec]
- Updated dependencies [97b57e7]
- Updated dependencies [3bdedf0]
- Updated dependencies [0da2b48]
  - @forge/storage@1.5.1
  - @forge/egress@1.2.0

## 2.15.2-next.3

### Patch Changes

- 97b57e7: Bumping dependencies via Renovate:

  - unzipper
  - @types/unzipper

- Updated dependencies [97b57e7]
  - @forge/storage@1.5.1-next.1

## 2.15.2-next.2

### Patch Changes

- Updated dependencies [3bdedf0]
  - @forge/egress@1.2.0-next.1

## 2.15.2-next.1

### Patch Changes

- 23a21ec: Bumping dependencies via Renovate:

  - portfinder

- Updated dependencies [23a21ec]
  - @forge/storage@1.5.1-next.0

## 2.15.2-next.0

### Patch Changes

- Updated dependencies [0da2b48]
  - @forge/egress@1.1.3-next.0

## 2.15.1

### Patch Changes

- e91cabc: Route HTTPS calls from the new runtime through @forge/api
- 55f66d2: Support fetch through proxy on the Node Runtime

## 2.15.1-next.1

### Patch Changes

- e91cabc: Route HTTPS calls from the new runtime through @forge/api

## 2.15.1-next.0

### Patch Changes

- 55f66d2: Support fetch through proxy on the Node Runtime

## 2.15.0

### Minor Changes

- e756563: Exports Sort order for custom entities query

### Patch Changes

- Updated dependencies [e756563]
  - @forge/storage@1.5.0

## 2.15.0-next.0

### Minor Changes

- e756563: Exports Sort order for custom entities query

### Patch Changes

- Updated dependencies [e756563]
  - @forge/storage@1.5.0-next.0

## 2.14.0

### Minor Changes

- c5f6d56: Bug fix for incorrect typing on storage method

### Patch Changes

- 526e78c: Add support for requestBitbucket on the Forge Node runtime

## 2.14.0-next.1

### Patch Changes

- 526e78c: Add support for requestBitbucket on the Forge Node runtime

## 2.14.0-next.0

### Minor Changes

- c5f6d56: Bug fix for incorrect typing on storage method

## 2.13.0

### Minor Changes

- 8577200: Added capabilities for enabling Custom entities

### Patch Changes

- 4acdb01: Support migrations API on Node runtime
- 0195732: Support reportPersonalData on Node runtime
- Updated dependencies [8577200]
  - @forge/storage@1.4.0

## 2.13.0-next.1

### Patch Changes

- 4acdb01c: Support migrations API on Node runtime
- 0195732c: Support reportPersonalData on Node runtime

## 2.13.0-next.0

### Minor Changes

- 85772008: Added capabilities for enabling Custom entities

### Patch Changes

- Updated dependencies [85772008]
  - @forge/storage@1.4.0-next.0

## 2.12.0

### Minor Changes

- 60a3be6: Add NodeJS runtime support for unauthenticated product requests via the api object

### Patch Changes

- 82ea466: Add metrics for NodeJS runtime
- 659107b: Implement getting webtrigger URL in Node runtime

## 2.12.0-next.2

### Patch Changes

- 659107b: Implement getting webtrigger URL in Node runtime

## 2.12.0-next.1

### Patch Changes

- 82ea466: Add metrics for NodeJS runtime

## 2.12.0-next.0

### Minor Changes

- 60a3be66: Add NodeJS runtime support for unauthenticated product requests via the api object

## 2.11.1

### Patch Changes

- 61d41c6: Bumping dependencies via Renovate:

  - @types/react-reconciler

- ba6d381: Bumping dependencies via Renovate:

  - @types/cross-spawn

- 4a070c4: Bumping dependencies via Renovate:

  - command-exists

- 37eee39: Bumping dependencies via Renovate:

  - ajv

- 444ada4: Bumping dependencies via Renovate:

  - @changesets/types

- 66da5d9: Bumping dependencies via Renovate:

  - conf

- decbf80: Bumping dependencies via Renovate:

  - @types/react

- 2b1589f: Bumping dependencies via Renovate:

  - @types/minimatch

- 6386221: Bumping dependencies via Renovate:

  - @types/lodash

- 203f465: Bumping dependencies via Renovate:

  - @types/inquirer

- 51d8225: Bumping dependencies via Renovate:

  - @types/xml

- c810119: Bumping dependencies via Renovate:

  - @atlassian/logger-interface

- d6d7226: Bumping dependencies via Renovate:

  - @types/history

- 5d6f948: Bumping dependencies via Renovate:

  - @changesets/apply-release-plan

- 0d52cdb: Bumping dependencies via Renovate:

  - @types/uuid

- 1508104: Bumping dependencies via Renovate:

  - @types/cheerio

- ff5d0f8: Bumping dependencies via Renovate:

  - @types/supertest

- 8970da8: Bumping dependencies via Renovate:

  - @types/concat-stream

- Updated dependencies [61d41c6]
- Updated dependencies [ba6d381]
- Updated dependencies [4a070c4]
- Updated dependencies [37eee39]
- Updated dependencies [444ada4]
- Updated dependencies [66da5d9]
- Updated dependencies [decbf80]
- Updated dependencies [2b1589f]
- Updated dependencies [6386221]
- Updated dependencies [203f465]
- Updated dependencies [51d8225]
- Updated dependencies [c810119]
- Updated dependencies [d6d7226]
- Updated dependencies [5d6f948]
- Updated dependencies [0d52cdb]
- Updated dependencies [1508104]
- Updated dependencies [ff5d0f8]
- Updated dependencies [8970da8]
  - @forge/storage@1.3.2

## 2.11.1-next.8

### Patch Changes

- 4a070c4: Bumping dependencies via Renovate:

  - command-exists

- 66da5d9: Bumping dependencies via Renovate:

  - conf

- decbf80: Bumping dependencies via Renovate:

  - @types/react

- Updated dependencies [4a070c4]
- Updated dependencies [66da5d9]
- Updated dependencies [decbf80]
  - @forge/storage@1.3.2-next.8

## 2.11.1-next.7

### Patch Changes

- 37eee39: Bumping dependencies via Renovate:

  - ajv

- Updated dependencies [37eee39]
  - @forge/storage@1.3.2-next.7

## 2.11.1-next.6

### Patch Changes

- 61d41c6: Bumping dependencies via Renovate:

  - @types/react-reconciler

- ba6d381: Bumping dependencies via Renovate:

  - @types/cross-spawn

- 203f465: Bumping dependencies via Renovate:

  - @types/inquirer

- ff5d0f8: Bumping dependencies via Renovate:

  - @types/supertest

- Updated dependencies [61d41c6]
- Updated dependencies [ba6d381]
- Updated dependencies [203f465]
- Updated dependencies [ff5d0f8]
  - @forge/storage@1.3.2-next.6

## 2.11.1-next.5

### Patch Changes

- 0d52cdb: Bumping dependencies via Renovate:

  - @types/uuid

- Updated dependencies [0d52cdb]
  - @forge/storage@1.3.2-next.5

## 2.11.1-next.4

### Patch Changes

- 51d8225: Bumping dependencies via Renovate:

  - @types/xml

- Updated dependencies [51d8225]
  - @forge/storage@1.3.2-next.4

## 2.11.1-next.3

### Patch Changes

- 2b1589f: Bumping dependencies via Renovate:

  - @types/minimatch

- 5d6f948: Bumping dependencies via Renovate:

  - @changesets/apply-release-plan

- Updated dependencies [2b1589f]
- Updated dependencies [5d6f948]
  - @forge/storage@1.3.2-next.3

## 2.11.1-next.2

### Patch Changes

- 6386221: Bumping dependencies via Renovate:

  - @types/lodash

- Updated dependencies [6386221]
  - @forge/storage@1.3.2-next.2

## 2.11.1-next.1

### Patch Changes

- 444ada4: Bumping dependencies via Renovate:

  - @changesets/types

- d6d7226: Bumping dependencies via Renovate:

  - @types/history

- 8970da8: Bumping dependencies via Renovate:

  - @types/concat-stream

- Updated dependencies [444ada4]
- Updated dependencies [d6d7226]
- Updated dependencies [8970da8]
  - @forge/storage@1.3.2-next.1

## 2.11.1-next.0

### Patch Changes

- c810119: Bumping dependencies via Renovate:

  - @atlassian/logger-interface

- 1508104: Bumping dependencies via Renovate:

  - @types/cheerio

- Updated dependencies [c810119]
- Updated dependencies [1508104]
  - @forge/storage@1.3.2-next.0

## 2.11.0

### Minor Changes

- 3a1dd86: Move API errors to @forge/api package
- df3811d: Handle NeedsAuthError returned from the proxy for Runtime v2

### Patch Changes

- dfab69c: Add NodeJS runtime support for Events API
- ceb1141: Add missing dependencies
- addbda1: Fix how user code error are being reported by Runtime v2
- Updated dependencies [aa19308]
  - @forge/egress@1.1.2

## 2.11.0-next.4

### Patch Changes

- ceb1141: Add missing dependencies

## 2.11.0-next.3

### Patch Changes

- addbda1: Fix how user code error are being reported by Runtime v2

## 2.11.0-next.2

### Minor Changes

- 3a1dd86: Move API errors to @forge/api package
- df3811d: Handle NeedsAuthError returned from the proxy for Runtime v2

## 2.10.1-next.1

### Patch Changes

- Updated dependencies [aa193085]
  - @forge/egress@1.1.2-next.0

## 2.10.1-next.0

### Patch Changes

- dfab69c: Add NodeJS runtime support for Events API

## 2.10.0

### Minor Changes

- e0e3587: Add NodeJS runtime support for Storage API

### Patch Changes

- 37f48c5: Fix contextAri for nodejs runtime
- ca8551d: Fix proxy fetch not respecting requestInit options

## 2.10.0-next.2

### Patch Changes

- 37f48c5: Fix contextAri for nodejs runtime

## 2.10.0-next.1

### Minor Changes

- e0e3587a: Add NodeJS runtime support for Storage API

## 2.9.2-next.0

### Patch Changes

- ca8551dd: Fix proxy fetch not respecting requestInit options

## 2.9.1

### Patch Changes

- 73b929a: Export routeFromAbsolute
- 8d0dc10: Use a single global to pass information from the runtime
- 7a4fa35: Support internet egress calls in the new runtime

## 2.9.1-next.2

### Patch Changes

- 8d0dc104: Use a single global to pass information from the runtime

## 2.9.1-next.1

### Patch Changes

- 73b929a: Export routeFromAbsolute

## 2.9.1-next.0

### Patch Changes

- 7a4fa35: Support internet egress calls in the new runtime

## 2.9.0

### Minor Changes

- e5dd325: support for requestBitbucket

## 2.9.0-next.0

### Minor Changes

- e5dd325d: support for requestBitbucket

## 2.8.1

### Patch Changes

- 8e2a5a6: Update node-fetch version
- Updated dependencies [8e2a5a6]
  - @forge/storage@1.3.1

## 2.8.1-next.0

### Patch Changes

- 8e2a5a6: Update node-fetch version
- Updated dependencies [8e2a5a6]
  - @forge/storage@1.3.1-next.0

## 2.8.0

### Minor Changes

- 95913f6: Call product APIs via proxy on Node runtime

## 2.8.0-next.0

### Minor Changes

- 95913f6: Call product APIs via proxy on Node runtime

## 2.7.0

### Minor Changes

- b3ee297: Unblock valid property API keys and handled error propagation

### Patch Changes

- Updated dependencies [3c3c42b]
  - @forge/storage@1.3.0

## 2.7.0-next.0

### Minor Changes

- b3ee2973: Unblock valid property API keys and handled error propagation

### Patch Changes

- Updated dependencies [3c3c42b9]
  - @forge/storage@1.3.0-next.0

## 2.6.1

### Patch Changes

- Updated dependencies [21e392d]
  - @forge/storage@1.2.0

## 2.6.1-next.0

### Patch Changes

- Updated dependencies [21e392d]
  - @forge/storage@1.2.0-next.0

## 2.6.0

### Minor Changes

- 8571c05: Add support for multiple accounts in external authentication

## 2.6.0-next.0

### Minor Changes

- 8571c05: Add support for multiple accounts in external authentication

## 2.5.0

### Minor Changes

- 70e9c8c: Enable auth providers api

### Patch Changes

- 32d11d1: Fix types on external auth APIs

## 2.5.0-next.1

### Patch Changes

- 32d11d1: Fix types on external auth APIs

## 2.5.0-next.0

### Minor Changes

- df2cd2f: Enable auth providers api

## 2.4.0

## 2.4.0-next.0

### Minor Changes

- de02d45: Enable auth providers api

## 2.3.0

### Minor Changes

- 0d7fe27: Support secret storage API

### Patch Changes

- df58629: Allow custom headers in requestGraph
- Updated dependencies [0d7fe27]
  - @forge/storage@1.1.0

## 2.3.0-next.1

### Patch Changes

- df58629: Allow custom headers in requestGraph

## 2.3.0-next.0

### Minor Changes

- 0d7fe27: Support secret storage API

### Patch Changes

- Updated dependencies [0d7fe27]
  - @forge/storage@1.1.0-next.0

## 2.2.1

### Patch Changes

- Updated dependencies [0700578]
  - @forge/storage@1.0.5

## 2.2.1-next.0

### Patch Changes

- Updated dependencies [0700578]
  - @forge/storage@1.0.5-next.0

## 2.2.0

### Minor Changes

- 5dcb9bd: Routes can be passed to api.fetch. Routes can be partially-constructed using other Routes.

## 2.2.0-next.0

### Minor Changes

- 5dcb9bd: Routes can be passed to api.fetch. Routes can be partially-constructed using other Routes.

## 2.1.0

### Minor Changes

- 339a8ad: Export StorageAPI interface
- 390e3d0: Add authorize API

### Patch Changes

- fef6d3a: Export Route as type for @forge/api
- 85ce23a: Update error message and build config
- Updated dependencies [5ff60ec]
- Updated dependencies [4eda18e]
- Updated dependencies [85ce23a]
- Updated dependencies [2d3bec6]
  - @forge/storage@1.0.4
  - @forge/auth@0.0.1

## 2.1.0-next.4

### Patch Changes

- 85ce23a: Update error message and build config
- Updated dependencies [85ce23a]
  - @forge/auth@0.0.1-next.2

## 2.1.0-next.3

### Minor Changes

- 390e3d0: Add authorize API

### Patch Changes

- Updated dependencies [4eda18e]
  - @forge/auth@0.0.1-next.0

## 2.1.0-next.2

### Patch Changes

- fef6d3a: Export Route as type for @forge/api

## 2.1.0-next.1

### Minor Changes

- 339a8ad: Export StorageAPI interface

## 2.0.2-next.0

### Patch Changes

- Updated dependencies [5ff60ec]
  - @forge/storage@1.0.4-next.0

## 2.0.1

### Patch Changes

- d3d180e: Remove engines limitation

## 2.0.1-next.0

### Patch Changes

- d3d180e: Remove engines limitation

## 2.0.0

### Major Changes

- 4ae62248a: `@forge/api` requires usage of a new route helper when calling requestJira/requestConfluence

## 1.2.0

### Minor Changes

- 2ede277: Add new getWebTriggerUrl runtime API method

### Patch Changes

- b0ae6aa: Fix properties onConfluenceSpace for personal spaces
- Updated dependencies [9b496aa]
  - @forge/storage@1.0.3

## 1.2.0-next.2

### Patch Changes

- Updated dependencies [9b496aa]
  - @forge/storage@1.0.3-next.0

## 1.2.0-next.1

### Minor Changes

- 2ede277: Add new getWebTriggerUrl runtime API method

## 1.1.1-next.0

### Patch Changes

- b0ae6aa: Fix properties onConfluenceSpace for personal spaces

## 1.1.0

### Minor Changes

- 2136fd7: Adds reportPersonalData method to forge help apps comply with GDPR
- ba442a3: Add requestGraph method to fetch api.

### Patch Changes

- Updated dependencies [c7c6de2]
  - @forge/storage@1.0.2

## 1.1.0-next.2

### Patch Changes

- Updated dependencies [c7c6de2]
  - @forge/storage@1.0.2-next.0

## 1.1.0-next.1

### Minor Changes

- ba442a3: Add requestGraph method to fetch api.

## 1.1.0-next.0

### Minor Changes

- 2136fd7: Adds reportPersonalData method to forge help apps comply with GDPR

## 1.0.1

### Patch Changes

- Updated dependencies [c0e9085]
  - @forge/storage@1.0.1

## 1.0.1-next.0

### Patch Changes

- Updated dependencies [c0e9085]
  - @forge/storage@1.0.1-next.0

## 1.0.0

### Major Changes

- 1daf2c5: Forge packages to 1.0.0 for upcoming platform GA 🎉

### Patch Changes

- Updated dependencies [1daf2c5]
  - @forge/storage@1.0.0

## 1.0.0-next.0

### Major Changes

- 1daf2c5: Forge is now generally available 🎉

### Patch Changes

- Updated dependencies [1daf2c5]
  - @forge/storage@1.0.0-next.0

## 0.7.0

### Minor Changes

- 843a703: Add forge-storage library

### Patch Changes

- Updated dependencies [843a703]
  - @forge/storage@0.0.2

## 0.7.0-next.0

### Minor Changes

- 843a703: Add forge-storage library

### Patch Changes

- Updated dependencies [843a703]
  - @forge/storage@0.0.2-next.0

## 0.6.1

### Patch Changes

- b41cc4c: Fix exported content
- 5b666a5: Fix published content
- 6f786cf: Fixing falsey values in storage api

## 0.6.1-next.2

### Patch Changes

- 5b666a5: Fix published content

## 0.6.1-next.1

### Patch Changes

- b41cc4c: Fix exported content

## 0.6.1-next.0

### Patch Changes

- 6f786cf: Fixing falsey values in storage api

## 0.6.0

### Minor Changes

- 8691270: Expose headers as Web API Headers object

### Patch Changes

- 8ee5500: Fix dependency resolution by adding node-fetch

## 0.6.0-next.1

### Patch Changes

- 8ee5500: Fix dependency resolution by adding node-fetch

## 0.6.0-next.0

### Minor Changes

- 8691270: Expose headers as Web API Headers object

## 0.5.5

### Patch Changes

- aedff38: Fix dependency ranges
- a5eed70: update node-fetch and types versions

## 0.5.5-next.1

### Patch Changes

- aedff38: Fix dependency ranges

## 0.5.5-next.0

### Patch Changes

- a5eed70: update node-fetch and types versions

## 0.5.4

## 0.5.3

### Patch Changes

- d3ed280: GlobalStorage API now raises an APIError if response status code is not 200
- d3ed280: GlobalStorage will raise an APIError if response code is not 200

## 0.5.3-next.0

### Patch Changes

- d3ed280: GlobalStorage API now raises an APIError if response status code is not 200
- d3ed280: GlobalStorage will raise an APIError if response code is not 200

## 0.5.2

## 0.5.1

### Patch Changes

- b248685: Remove repo url from packages

## 0.5.1-next.0

### Patch Changes

- b248685: Remove repo url from packages

## 0.5.0

### Minor Changes

- 84eeb25: Split storage and properties API. Mark storage API as stable. Mark properties API methods in storage API as deprecated.

### Patch Changes

- f383ae2: Simplify store api deprecation message
- 7f210ee: Introduce 'storage' API for global storage APIs
- b22b1a5: Fix .onConfluenceSpace() api
- a91f2c6: Improved error reporting for store API

## 0.5.0-next.4

### Patch Changes

- f383ae2: Simplify store api deprecation message

## 0.5.0-next.3

### Patch Changes

- b22b1a5: Fix .onConfluenceSpace() api

## 0.5.0-next.2

### Patch Changes

- 7f210ee: Introduce 'storage' API for global storage APIs

## 0.5.0-next.1

### Minor Changes

- 84eeb25: Split storage and properties API. Mark storage API as stable. Mark properties API methods in storage API as deprecated.

## 0.4.3-next.0

### Patch Changes

- a91f2c6: Improved error reporting for store API

## 0.4.2

## 0.4.1

## 0.4.0

### Minor Changes

- 3139910: Add query API to the global storage API
- 41547fa: Move experimental APIs to an experimental import
- 77307e0: Implement + refactor global storage API

### Patch Changes

- 8a41f17: Make Global Storage implementation compatible with snapshot

## 0.4.0-next.2

### Minor Changes

- 3139910: Add query API to the global storage API
- 41547fa: Move experimental APIs to an experimental import

## 0.4.0-next.1

### Patch Changes

- 8a41f17: Make Global Storage implementation compatible with snapshot

## 0.4.0-next.0

### Minor Changes

- 77307e0: Implement + refactor global storage API

## 0.3.0

### Minor Changes

- c59238c: Revert Global Storage API changes

## 0.3.0-next.1

### Minor Changes

- c59238c: Revert Global Storage API changes

## 0.3.0-next.0

### Minor Changes

- 4c7ab2f: Implement new global storage API

## 0.2.0

### Minor Changes

- ec9ae41: Move Storage API from bootstrap into forge-api package

### Patch Changes

- f112c64: Inject the Storage API into the forge-api package default exported object

## 0.2.0-next.1

### Patch Changes

- f112c64: Inject the Storage API into the forge-api package default exported object

## 0.2.0-next.0

### Minor Changes

- ec9ae41: Move Storage API from bootstrap into forge-api package

## 0.1.7

## 0.1.6

### Patch Changes

- cedd68a: Stopped http polyfill passing through all arguments to fetch bridge method

## 0.1.6-next.0

### Patch Changes

- cedd68a: Stopped http polyfill passing through all arguments to fetch bridge method

## 0.1.5

### Patch Changes

- f86d998: Added types for the store methods
- f6d1694: Added onJiraProject to the Storage API

## 0.1.4
